<!DOCTYPE html>
<html lang="en">
<head>     <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico?v=2">
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Blog Post - Sahla Smart Solutions</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            light: '#f9f9f9',
            dark: '#111827',
            text: '#333333',
            'light-text': '#eeeeee',
          },
          borderRadius: {
            button: '9999px',
          },
        },
      },
      // The line below was causing issues - typography needs to be properly included, not referenced as a plugin
      // plugins: [tailwind.typography],
    };
  </script>
  <!-- Add Tailwind Typography plugin properly -->
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/typography@0.5.9/dist/typography.min.css"></script>
      <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/navigation.css">
  <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet" />

  <!-- Load marked.js for Markdown rendering -->
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

  <!-- Load gray-matter from Skypack for frontmatter parsing -->
  <script type="module">
    import matter from 'https://cdn.skypack.dev/gray-matter';
  
    document.addEventListener("DOMContentLoaded", async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const slug = urlParams.get("post");
  
      if (!slug) {
        document.querySelector(".post-content").innerHTML = "<p>Post not found.</p>";
        return;
      }
  
      try {
        const response = await fetch(`../blog-posts/${slug}.md`);
        if (!response.ok) throw new Error("Post not found");
  
        const markdown = await response.text();
        const parsed = matter(markdown);
        const content = parsed.content;
        const data = parsed.data;
  
        // Set title
        document.querySelector(".post-title").textContent = data.title || "Untitled Post";
  
        // Set background image if available
        if (data.cover_image) {
          const postHeader = document.querySelector(".post-header");
          postHeader.style.backgroundImage = `url('../assets/images/blog/${data.cover_image}')` || url('../assets/images/blog-placeholder.webp');
          postHeader.style.backgroundSize = 'cover';
          postHeader.style.backgroundPosition = 'center';
        }
  
        // Configure marked
        marked.setOptions({
          headerIds: true,
          mangle: false
        });
  
        // Render content
        document.querySelector(".post-content").innerHTML = marked.parse(content);
  
      } catch (error) {
        console.error(error);
        document.querySelector(".post-content").innerHTML = "<p>Post not found or could not be rendered.</p>";
      }
    });
  </script>
  

  <!-- Add custom styles to override Tailwind reset for Markdown content -->
  <style>
    /* Header Styles */
    .header {
      background-color: var(--background-color);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: sticky;
      top: 0;
      z-index: 1000;
    }

    .nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 2rem;
    }

    .nav-logo img {
      height: 40px;
    }

    .nav-links {
      display: flex;
      align-items: center;
      gap: 1.5rem;
    }

    .nav-link {
      color: var(--text-color);
      text-decoration: none;
      font-weight: 500;
      transition: color 0.3s ease;
      position: relative;
    }

    .nav-link:hover {
      color: var(--primary-color-1);
    }


    .mobile-menu-toggle {
      display: none;
    }

    .mobile-menu-button {
      display: none;
    }

    /* Markdown content styling */
    .prose {
      max-width: 65ch;
      margin: 0 auto;
    }

    .prose h1, 
    .prose h2, 
    .prose h3, 
    .prose h4 {
      color: inherit;
      margin-top: 1.5em;
      margin-bottom: 0.5em;
      font-weight: 600;
    }

    .prose h1 {
      color: var(--text-color);
      font-size: 2.25rem;
    }

    .prose h2 {
      color: var(--text-color);
      font-size: 1.875rem;
    }

    .prose h3 {
      color: var(--text-color);
      font-size: 1.5rem;
    }

    .prose p {
      color: var(--text-color);
      margin-top: 1em;
      margin-bottom: 1em;
    }

    .prose ul, 
    .prose ol {
      margin-top: 1em;
      margin-bottom: 1em;
      padding-left: 1.5em;
    }

    .prose ul {
      list-style-type: disc;
    }

    .prose ol {
      list-style-type: decimal;
    }

    .prose li {
      color: var(--text-color);
      margin-top: 0.5em;
      margin-bottom: 0.5em;
    }

    .prose a {
      color: #3182ce;
      text-decoration: underline;
    }

    .prose blockquote {
      border-left: 4px solid #e2e8f0;
      padding-left: 1em;
      margin-left: 0;
      font-style: italic;
    }

    .prose code {
      background-color: #f7fafc;
      padding: 0.2em 0.4em;
      border-radius: 0.25em;
      font-size: 0.875em;
    }

    .prose pre {
      background-color: #f7fafc;
      padding: 1em;
      border-radius: 0.25em;
      overflow-x: auto;
      margin-top: 1em;
      margin-bottom: 1em;
    }

    .prose pre code {
      background-color: transparent;
      padding: 0;
    }

    .prose img {
      max-width: 100%;
      height: auto;
      margin-top: 1em;
      margin-bottom: 1em;
    }

    .prose table {
      width: 100%;
      table-layout: auto;
      text-align: left;
      border-collapse: collapse;
      margin-top: 1em;
      margin-bottom: 1em;
    }

    .prose th {
      font-weight: 600;
      border-bottom: 1px solid #e2e8f0;
      padding: 0.5em;
    }

    .prose td {
      border-bottom: 1px solid #e2e8f0;
      padding: 0.5em;
    }

    /* Dark mode styles for prose */
    .dark .prose {
      color: #e2e8f0;
    }

    .dark .prose a {
      color: #90cdf4;
    }

    .dark .prose code {
      background-color: #2d3748;
    }

    .dark .prose pre {
      background-color: #2d3748;
    }

    .dark .prose blockquote {
      border-left-color: #4a5568;
    }

    .dark .prose th,
    .dark .prose td {
      border-bottom-color: #4a5568;
    }

    @media (max-width: 768px) {
      .nav-links {
        display: none;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--background-color);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .mobile-menu-toggle:checked + .mobile-menu-button + .nav-links {
        display: flex;
      }

      .mobile-menu-button {
        display: block;
        cursor: pointer;
      }

      .mobile-menu-button span {
        display: block;
        width: 25px;
        height: 3px;
        margin: 5px auto;
        background-color: var(--text);
        transition: all 0.3s ease;
      }
      
      .prose {
        padding-left: 1rem;
        padding-right: 1rem;
      }
    }
  </style>
  <!-- MailerLite Universal -->
  <script>
    (function(w,d,e,u,f,l,n){w[f]=w[f]||function(){(w[f].q=w[f].q||[])
    .push(arguments);},l=d.createElement(e),l.async=1,l.src=u,
    n=d.getElementsByTagName(e)[0],n.parentNode.insertBefore(l,n);})
    (window,document,'script','https://assets.mailerlite.com/js/universal.js','ml');
    ml('account', '1446707');
  </script>
<!-- End MailerLite Universal -->
</head>
<body class="bg-light dark:bg-dark text-text dark:text-light-text">
  <!-- Header -->
    <header class="header">
        <div class="container">
            <nav class="nav">
                <a href="./index.html" class="nav-logo">
                    <img src="./assets/images/Main Horizontal Version.svg" alt="Sahla Logo" class="logo-img" id="nav-logo">
                </a>
                <input type="checkbox" id="mobile-menu-toggle" class="mobile-menu-toggle">
                <label for="mobile-menu-toggle" class="mobile-menu-button">
                    <span></span>
                    <span></span>
                    <span></span>
                </label>
                <div class="nav-links">
                    <div class="dropdown">
                        <a href="#services" class="nav-link">Services</a>
                        <div class="dropdown-content">
                            <a href="./process-automation.html" class="dropdown-link">Process Automation</a>
                            <a href="./technology-consulting.html" class="dropdown-link">Technology Consulting</a>
                        </div>
                    </div>
                    <a href="./ventures.html" class="nav-link">Our Ventures</a>
                    <a href="./partners.html" class="nav-link">Partners</a>
                    <a href="./careers.html" class="nav-link">Careers</a>
                    <a href="./blog.html" class="nav-link">Blog</a>
                    <a href="./about.html" class="nav-link">About us</a>
                    <div class="px-4">
                        <div class="h-6 border-l-2 border-gray-300"></div>
                    </div>
                    <a href="./contact.html" class="bg-light px-6 py-2 !rounded-button hover:bg-opacity-90 transition-all">Contact Sales</a>
                    <div class="hidden md:flex items-center gap-4">
                        <div class="theme-switch" id="theme-switch">
                            <i class="ri-moon-line moon-icon"></i>
                            <i class="ri-sun-line sun-icon"></i>
                            <script>
                                (() => {
                                    const savedTheme = localStorage.getItem('theme');
                                    const preferred = savedTheme || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                                    document.documentElement.setAttribute('data-theme', preferred);
                                })();
                            </script>
                        </div>
                    </div>
                </div>
            </nav>
        </div>
    </header>
  <!-- Blog Post Content -->
   <!-- Post Header -->
<div class="post-header relative w-full h-64 flex items-end p-6 text-white" style="padding-inline: 12%;">
    <h1 class="post-title text-5xl font-bold z-10"></h1>
    <div class="absolute inset-0 bg-black/40 z-0"></div>
  </div>
  
  <!-- Post Content -->
  <div class="post-content prose dark:prose-invert max-w-none p-6 mx-auto pb-[7rem] " style="background-color: var(--background-color-light); padding-inline: 12%;"></div>
  <!-- <main class="container mx-auto px-4 py-16">
    <article class="prose lg:prose-xl mx-auto">
      <h1 class="post-title text-4xl font-bold mb-4"></h1>
      <div class="post-content"></div>
    </article>
  </main> -->

  <!-- Footer -->
<!-- f -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Company Info Column -->
                <div class="footer-info">
                    <a href="../index.html" class="footer-logo">
                        <img src="../assets/images/Negative Horizontal Version.svg" alt="Sahla Smart Solutions" class="logo-img" id="foot-logo">
                    </a>
                    <p class="footer-description">Empowering businesses through innovative venture building and automation solutions in the MENA region.</p>
                    <div class="footer-social">
                        <a href="https://eg.linkedin.com/company/sahla-solutions" target="_blank" rel="noopener noreferrer"><i class="ri-linkedin-fill"></i></a>
                        <a href="https://x.com/sahlasolutions" target="_blank" rel="noopener noreferrer"><i class="ri-twitter-fill"></i></a>
                        <a href="https://www.facebook.com/SahlaSmartSolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-facebook-fill"></i></a>
                        <a href="https://www.instagram.com/sahlasolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-instagram-fill"></i></a>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div class="footer-links-column">
                    <h4>Quick Links</h4>
                    <ul class="footer-links-list">
                        <li><a href="../index.html">Home</a></li>
                        <li><a href="./about.html">About Us</a></li>
                        <li><a href="./ventures.html">Our Ventures</a></li>
                        <li><a href="./partners.html">Partners</a></li>
                        <li><a href="./careers.html">Careers</a></li>
                        <li><a href="./contact.html">Contact Us</a></li>
                    </ul>
                </div>

                <!-- Services Column -->
                <div class="footer-links-column">
                    <h4>Services</h4>
                    <ul class="footer-links-list">
                        <li><a href="./process-automation.html">Process Automation</a></li>
                        <li><a href="./technology-consulting.html">Technology Consulting</a></li>
                    </ul>
                </div>

                <!-- Newsletter Column -->
                <div class="footer-newsletter">
                    <h4>Newsletter</h4>

                    <form class="mb-4">
                        <div class="flex">
                            <!-- <input type="email" placeholder="Your email" class="w-full px-4 py-2 bg-gray-800 border-none text-white text-sm rounded-l"> -->
                            <a href="javascript:void(0)" class="ml-onclick-form" onclick="ml('show', 'gturk0', true)" style="
                                display: inline-block;
                                padding: 8px 16px;
                                background-color: var(--primary-color-1);
                                color: white;
                                font-size: 16px;
                                font-family: sans-serif;
                                border: none;
                                border-radius: 6px;
                                text-decoration: none;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                                " onmouseover="this.style.backgroundColor='var(--primary-color-2)'" onmouseout="this.style.backgroundColor= 'var(--primary-color-3)'">
                                Subscribe to Newsletter
                                </a>
                        </div>
                    </form>
                    <p>Subscribe to our newsletter to stay updated with our latest news and insights.</p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0">© 2025 Sahla Smart Solutions. All rights reserved.</p>
                    <div class="flex space-x-6">
                        <a href="./pages/privacy-policy.html" class="text-gray-400 hover:text-white text-sm">Privacy Policy</a>
                        <a href="./pages/terms-of-service.html" class="text-gray-400 hover:text-white text-sm">Terms of Service</a>
                        <a href="./pages/cookies-policy.html" class="text-gray-400 hover:text-white text-sm">Cookies Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

  <script>
    // Theme switch functionality
    const themeSwitch = document.getElementById('theme-switch');
    const html = document.documentElement;
    const navLogo = document.getElementById('nav-logo');
    const footLogo = document.getElementById('foot-logo');

    // Function to set and save theme
    const setTheme = (theme) => {
      html.setAttribute('data-theme', theme);
      localStorage.setItem('theme', theme);
      themeSwitch.classList.toggle('dark', theme === 'dark');

      // Update logo based on theme
      if (theme === 'dark') {
        navLogo.src = '../assets/images/Dark Horizontal Version.svg';
        footLogo.src = '../assets/images/Dark Horizontal Version.svg';
      } else {
        navLogo.src = '../assets/images/Main Horizontal Version.svg';
        footLogo.src = '../assets/images/Main Horizontal Version.svg';
      }
    };

    // Set the correct theme class on icon container and logo after load
    document.addEventListener('DOMContentLoaded', () => {
      const currentTheme = html.getAttribute('data-theme');
      themeSwitch.classList.toggle('dark', currentTheme === 'dark');

      // Set initial logo based on theme
      if (currentTheme === 'dark') {
        navLogo.src = '../assets/images/Dark Horizontal Version.svg';
        footLogo.src = '../assets/images/Dark Horizontal Version.svg';
      } else {
        navLogo.src = '../assets/images/Main Horizontal Version.svg';
        footLogo.src = '../assets/images/Main Horizontal Version.svg';
      }
    });

    // Toggle theme on click
    themeSwitch.addEventListener('click', () => {
      const isCurrentlyDark = html.getAttribute('data-theme') === 'dark';
      const newTheme = isCurrentlyDark ? 'light' : 'dark';
      setTheme(newTheme);
    });

    // Watch for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (!localStorage.getItem('theme')) {
        setTheme(e.matches ? 'dark' : 'light');
      }
    });

    // Mobile menu toggle
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.querySelector('.nav-links');

    mobileMenuToggle.addEventListener('change', function() {
      if (this.checked) {
        navLinks.style.display = 'flex';
      } else {
        navLinks.style.display = '';
      }
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function(e) {
        e.preventDefault();

        const targetId = this.getAttribute('href');
        if (targetId === '#') return;

        const targetElement = document.querySelector(targetId);
        if (targetElement) {
          const headerHeight = 80; // Approximate header height
          const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight;

          window.scrollTo({
            top: targetPosition,
            behavior: 'smooth'
          });

          // Close mobile menu if open
          if (mobileMenuToggle.checked) {
            mobileMenuToggle.checked = false;
            navLinks.style.display = '';
          }
        }
      });
    });

    // Sticky header effect
    window.addEventListener('scroll', () => {
      const header = document.querySelector('header');
      if (window.scrollY > 50) {
        header.classList.add('shadow-md');
      } else {
        header.classList.remove('shadow-md');
      }
    });

    // Newsletter form submission
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
      newsletterForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const email = e.target.querySelector('input[type="email"]').value;
        // Add your newsletter subscription logic here
        alert('Thank you for subscribing to our newsletter!');
        e.target.reset();
      });
    }
  </script>
</body>
</html>