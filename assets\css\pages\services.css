/* Services page specific styles */
.service-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(0, 86, 179, 0.1), 0 10px 10px -5px rgba(0, 86, 179, 0.04);
}

.service-icon {
    width: 4rem;
    height: 4rem;
    background-color: rgba(0, 86, 179, 0.1);
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.service-icon i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

/* Service section spacing */
.service-section {
    padding: 5rem 0;
}

/* Service grid layout */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

/* Service card content */
.service-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
}

.service-description {
    color: var(--text-gray);
    margin-bottom: 1.5rem;
    text-align: center;
}

.service-link {
    color: var(--primary-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: color 0.3s ease;
}

.service-link:hover {
    color: #004a99;
}

.service-link i {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.service-link:hover i {
    transform: translateX(4px);
} 