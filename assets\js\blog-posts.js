// Blog posts data structure
const blogPosts = {
    posts: [],
    categories: new Set(),
    tags: new Set(),
    authors: new Set()
};

// Function to parse frontmatter from markdown content
function parseFrontmatter(content) {
    const frontmatterRegex = /^---\n([\s\S]*?)\n---/;
    const match = content.match(frontmatterRegex);
    
    if (!match) return null;
    
    const frontmatter = match[1];
    const metadata = {};
    
    frontmatter.split('\n').forEach(line => {
        const [key, ...value] = line.split(':');
        if (key && value.length) {
            metadata[key.trim()] = value.join(':').trim().replace(/^["']|["']$/g, '');
        }
    });
    
    return metadata;
}

async function loadBlogPosts() {
    try {
        // Fetch the list of blog filenames
        const fileListResponse = await fetch('../../blog-posts/index.json');
        const filenames = await fileListResponse.json();
        
        // Fetch and parse all blog posts
        const posts = await Promise.all(filenames.map(async (filename) => {
            try{
                const res = await fetch(`../../blog-posts/${filename}`);
                const text = await res.text();
                const { content, data } = parseMarkdown(text); // Assuming `parseMarkdown()` is implemented

            return {
                    ...data,
                    content,
                    slug: filename.replace('.md', '')
                };
            } catch (error) {
                console.error(`Error processing blog post ${filename}:`, error);
                return null;
            }
        }));

        // Initialize global `blogPosts` object if not already
        blogPosts.posts = posts;
        blogPosts.categories = new Set();
        blogPosts.tags = new Set();
        blogPosts.authors = new Set();

        // Function to clean up metadata fields (remove extra quotes and trim spaces)
        const cleanUp = (value) => {
            if (typeof value === 'string') {
                return value.replace(/["\s]/g, ''); // Remove all quotation marks and spaces
            }
            return value;
        };

        // Extract categories, tags, and authors
        blogPosts.posts.forEach(post => {
            // Clean up the metadata fields
            post.title = post.title;
            post.author = post.author;
            post.category = post.category;
            post.summary = post.summary;
            post.canonical_url = post.canonical_url;
            post.linkedin_excerpt = post.linkedin_excerpt;
            post.cover_image = post.cover_image;
            post.release_date = post.release_date;
            post.reading_time = post.reading_time;
            post.draft = post.draft;

            // Clean up tags if they are present
            if (post.tags && Array.isArray(post.tags)) {
                post.tags = post.tags.map(tag => (tag));
                // Add tags to the global set
                post.tags.forEach(tag => blogPosts.tags.add(tag));
            }

            // Add to global sets (category, author)
            if (post.category) blogPosts.categories.add(post.category);
            if (post.author) blogPosts.authors.add(post.author);
        });

        // Sort posts by date
        blogPosts.posts.sort((a, b) => new Date(b.release_date) - new Date(a.release_date));

        return blogPosts;
    } catch (error) {
        console.error('Error loading blog posts:', error);
        return null;
    }
}

// Function to get featured posts
function getFeaturedPosts(limit = 3) {
    return blogPosts.posts
        .filter(post => !post.draft)
        .slice(0, limit);
}

// Function to get posts by category
function getPostsByCategory(category) {
    return blogPosts.posts.filter(post => 
        post.category === category && !post.draft
    );
}

// Function to get posts by tag
function getPostsByTag(tag) {
    return blogPosts.posts.filter(post => 
        post.tags.includes(tag) && !post.draft
    );
}

// Function to get posts by author
function getPostsByAuthor(author) {
    return blogPosts.posts.filter(post => 
        post.author === author && !post.draft
    );
}

// Function to search posts
function searchPosts(query) {
    if (!query) return blogPosts.posts;
    
    const searchTerms = query.toLowerCase().split(' ');
    return blogPosts.posts.filter(post => {
        const searchableText = `
            ${post.title} 
            ${post.summary} 
            ${post.category} 
            ${post.tags.join(' ')} 
            ${post.author}
        `.toLowerCase();
        
        return searchTerms.every(term => searchableText.includes(term));
    });
}

// Function to get related posts
function getRelatedPosts(currentPost, limit = 3) {
    return blogPosts.posts
        .filter(post => 
            post.slug !== currentPost.slug && 
            !post.draft &&
            (post.category === currentPost.category ||
             post.tags.some(tag => currentPost.tags.includes(tag)))
        )
        .slice(0, limit);
}

// Function to get recent posts
function getRecentPosts(limit = 5) {
    return blogPosts.posts
        .filter(post => !post.draft)
        .slice(0, limit);
}

// Function to get popular tags
function getPopularTags(limit = 10) {
    const tagCounts = new Map();
    
    blogPosts.posts.forEach(post => {
        post.tags.forEach(tag => {
            tagCounts.set(tag, (tagCounts.get(tag) || 0) + 1);
        });
    });
    
    return Array.from(tagCounts.entries())
        .sort((a, b) => b[1] - a[1])
        .slice(0, limit)
        .map(([tag, count]) => ({ tag, count }));
}

// Function to render post card
function renderPostCard(post) {
    return `
        <div class="blog-card">
            <div class="blog-image">
                <img src="../assets/images/blog/${post.cover_image}" alt="${post.title}" onerror="this.src='/assets/images/blog-placeholder.webp'" style="width: 100%; height: 200px; object-fit: cover;">
            </div>
            <div class="blog-content">               
                <h3 class="h3">${post.title}</h3>
                <div class="blog-meta">
                    <span><i class="ri-calendar-line"></i> ${new Date(post.release_date).toLocaleDateString('en-US', {
                        year: 'numeric', month: 'long', day: 'numeric'
                    })}</span>
                    <span><i class="ri-user-line"></i> ${post.author}</span>
                </div>
                 <div class="blog-tags">
                    ${[post.category, ...(post.tags || [])].map(tag => `
                        <span class="blog-tag">${tag}</span>
                    `).join('')}
                </div>

                <p class="blog-excerpt">${post.summary}</p>
                <a href="./blog-post.html?post=${post.slug}" class="btn btn-primary text-sm px-4 py-2 rounded"
                   style="width: 120px; margin: 0 auto; text-align: center;">Read More</a>
            </div>
        </div>
    `;
}


function renderCategoryCard(category) {
    const postCount = getPostsByCategory(category).length;

    return `
        <button class="category-filter-btn flex items-center justify-between w-full bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium hover:bg-gray-200 transition" 
                style="background-color: var(--background-color-light); color: var(--text-color);"
                data-category="${category}">
            <span>${category}</span>
            <span class="ml-2 inline-flex items-center justify-center w-6 h-6 text-xs font-semibold text-gray-800 bg-gray-300 rounded-full">
                ${postCount}
            </span>
        </button>
    `;
}


// Initialize blog functionality
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Load blog posts
        const result = await loadBlogPosts();
        if (!result) {
            console.error('Failed to load blog posts');
            return;
        }
        
        // Initialize recent posts
        await displayRecentPosts();
        renderPaginatedBlogPosts();
        // Render featured posts
        const featuredPostsContainer = document.querySelector('.featured-posts .grid');
        if (featuredPostsContainer) {
            const featuredPosts = getFeaturedPosts(3);
            featuredPostsContainer.innerHTML = featuredPosts.map(renderPostCard).join('');
        }
        
        // Render categories
        const categoriesContainer = document.querySelector('.blog-categories .category');
        if (categoriesContainer) {
            // Add "All Posts" button at the beginning
            categoriesContainer.innerHTML = `
                <button class="category-filter-btn active flex items-center justify-between w-full bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-medium hover:bg-gray-200 transition" 
                        style="background-color: var(--background-color-light); color: var(--text-color);"
                        data-category="all">
                    <span>All Posts</span>
                    <span class="ml-2 inline-flex items-center justify-center w-6 h-6 text-xs font-semibold text-gray-800 bg-gray-300 rounded-full">
                        ${blogPosts.posts.length}
                    </span>
                </button>
            ` + Array.from(blogPosts.categories)
                .map(renderCategoryCard)
                .join('');
                
            // Add event listeners to category filter buttons
            document.querySelectorAll('.category-filter-btn').forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active class from all buttons
                    document.querySelectorAll('.category-filter-btn').forEach(btn => {
                        btn.classList.remove('active');
                    });
                    
                    // Add active class to clicked button
                    button.classList.add('active');
                    
                    const category = button.getAttribute('data-category');
                    const postsContainer = document.querySelector('.blog-posts');
                    
                    if (postsContainer) {
                        if (category === 'all') {
                            // Show all posts
                            renderPaginatedBlogPosts();
                        } else {
                            // Filter by category
                            const filteredPosts = getPostsByCategory(category);
                            postsContainer.innerHTML = filteredPosts.map(renderPostCard).join('');
                        }
                    }
                });
            });
        }
        
        // Initialize search functionality
        const searchInput = document.querySelector('.search-box input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();
                const searchResults = searchPosts(query);
                const postsContainer = document.querySelector('.blog-posts');
                if (postsContainer) {
                    postsContainer.innerHTML = searchResults.map(renderPostCard).join('');
                }
            });
        }
        
        // Initialize tag cloud
        const tagCloudContainer = document.querySelector('.tag-cloud .flex');
        if (tagCloudContainer) {
            const popularTags = getPopularTags();
            tagCloudContainer.innerHTML = popularTags.map(({ tag, count }) => `
                <button class="tag-filter-btn" data-tag="${tag}">
                    ${tag}
                </button>
            `).join('');
            
            // Add event listeners to tag filter buttons
            document.querySelectorAll('.tag-filter-btn').forEach(button => {
                button.addEventListener('click', () => {
                    const tag = button.getAttribute('data-tag');
                    const filteredPosts = getPostsByTag(tag);
                    const postsContainer = document.querySelector('.blog-posts');
                    if (postsContainer) {
                        postsContainer.innerHTML = filteredPosts.map(renderPostCard).join('');
                    }
                });
            });
        }
        
        // Render all posts by default
        await renderPaginatedBlogPosts();
    } catch (error) {
        console.error('Error initializing blog functionality:', error);
    }
});


// Function to parse markdown content
function parseMarkdown(markdown) {
    const frontmatterRegex = /---\n([\s\S]+?)\n---/;
    const match = frontmatterRegex.exec(markdown);
    const frontmatter = match ? match[1] : '';
    const content = markdown.replace(frontmatterRegex, '').trim();
    const data = frontmatter.split('\n').reduce((acc, line) => {
        if (!line.trim()) return acc; // skip empty lines
    
        const [key, ...rest] = line.split(':');
        if (!key || !rest.length) return acc;
    
        const rawValue = rest.join(':').trimStart(); // only trim start to preserve intentional spacing
        
        // Remove trailing commas without trimming the rest
        let value = rawValue.endsWith(',') ? rawValue.slice(0, -1) : rawValue;
    
        // Parse JSON-like arrays
        if (value.startsWith('[') && value.endsWith(']')) {
            try {
                acc[key.trim()] = JSON.parse(value);
            } catch {
                acc[key.trim()] = value;
            }
        } else if (value === 'true' || value === 'false') {
            acc[key.trim()] = value === 'true';
        } else if (!isNaN(value) && !isNaN(parseFloat(value))) {
            acc[key.trim()] = Number(value);
        } else {
            // Preserve whitespace inside strings, just remove wrapping quotes
            if ((value.startsWith('"') && value.endsWith('"')) ||
                (value.startsWith("'") && value.endsWith("'"))) {
                value = value.slice(1, -1);
            }
            acc[key.trim()] = value;
        }
        
    
        return acc;
    }, {});
    return { content, data };
}


// Function to render a single blog post
async function renderBlogPost() {
    const params = new URLSearchParams(window.location.search);
    const slug = params.get('post');
    if (!slug) return;

    const response = await fetch(`/blog-posts/${slug}.md`);
    const markdown = await response.text();
    const { content, data } = parseMarkdown(markdown);

    document.querySelector('.post-title').textContent = data.title;
    document.querySelector('.post-content').innerHTML = marked(content);
}

// Initialize blog post rendering if on blog post page
if (window.location.pathname.includes('blog-post.html')) {
    renderBlogPost();
}

// Function to fetch and display recent posts
async function displayRecentPosts() {
    const posts = blogPosts.posts;
    const recentPosts = posts.slice(0, 3);
    const container = document.querySelector('.recent-posts');
  
    container.innerHTML = recentPosts.map(post => `
        <a href="./blog-post.html?post=${post.slug}" class="recent-post flex items-start gap-4 mb-4 no-underline text-inherit">
          <div class="recent-post-image w-[60px] h-[60px] overflow-hidden rounded-md shrink-0">
            <img src="../assets/images/blog/${post.cover_image}" alt="${post.title}" class="w-[70px] h-[70px] object-cover" />
          </div>
          <div class="recent-post-content text-sm">
            <h4 class="font-semibold leading-snug line-clamp-2">${post.title}</h4>
            <span class="text-gray-500 text-xs">${post.release_date}</span>
          </div>
        </a>
      `).join('');
      
      
  }
  
// Function to render paginated blog posts
async function renderPaginatedBlogPosts(page = 1, postsPerPage = 6) {
    const posts = blogPosts.posts;
    const totalPages = Math.ceil(posts.length / postsPerPage);
    const start = (page - 1) * postsPerPage;
    const end = start + postsPerPage;
    const paginatedPosts = posts.slice(start, end);

    const container = document.querySelector('.blog-posts');
    container.innerHTML = paginatedPosts.map(post => renderPostCard(post)).join('');

    // Pagination controls
    const paginationContainer = document.querySelector('.pagination');
    paginationContainer.innerHTML = Array.from({ length: totalPages }, (_, i) => `
        <button class="pagination-btn ${i + 1 === page ? 'active' : ''}" data-page="${i + 1}">${i + 1}</button>
    `).join('');

    // Add event listeners to pagination buttons
    document.querySelectorAll('.pagination-btn').forEach(button => {
        button.addEventListener('click', (e) => {
            const page = parseInt(e.target.dataset.page);
            renderPaginatedBlogPosts(page, postsPerPage);
        });
    });
}
