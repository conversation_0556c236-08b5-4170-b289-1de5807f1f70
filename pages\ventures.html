<!DOCTYPE html>
<html lang="en">

<head>     <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico?v=2">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Ventures - Sahla Smart Solutions</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0056b3',
                        secondary: '#4dabf7'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/navigation.css">

    <!-- Dynamic Font Loading -->
    <link id="google-fonts" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- i18next Core and HTTP Backend -->
    <script src="https://unpkg.com/i18next@23.7.16/dist/umd/i18next.min.js"></script>
    <script src="https://unpkg.com/i18next-http-backend@2.4.2/i18nextHttpBackend.min.js"></script>

    <script src="../assets/js/ventures-data.js"></script>
    <!-- Load marked.js for Markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
         :where([class^="ri-"])::before {
            content: "\f3c2";
        }

        .ventures-hero {
            background-image: linear-gradient(to bottom, #111827d8, #22267a8f), url('https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg');
            background-size: cover;
            background-position: center;
        }

        .venture-card {
            background-color: var(--background-color);
            border-radius: 1rem;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .venture-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-development {
            background-color: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .status-planning {
            background-color: rgba(107, 114, 128, 0.1);
            color: #6b7280;
        }

        .status-operational {
            background-color: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .operating-model-step {
            position: relative;
            padding-left: 2rem;
            margin-bottom: 1.5rem;
        }

        .operating-model-step::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0.5rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background: var(--primary-color);
        }

        .operating-model-step::after {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 1.5rem;
            bottom: -1.5rem;
            width: 2px;
            background: var(--primary-color);
        }

        .operating-model-step:last-child::after {
            display: none;
        }

        .upcoming-venture {
            background-color: var(--background-color-light);
            border: 1px solid var(--border-color);
            border-radius: 1rem;
            padding: 1.5rem;
            transition: transform 0.3s ease;
        }

        .upcoming-venture:hover {
            transform: translateY(-5px);
        }
        /* Modal styles */

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .modal.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background-color: var(--background-color);
            border-radius: 1rem;
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            padding: 2rem;
            position: relative;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .close-modal {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 1.5rem;
            color: var(--text-color);
            cursor: pointer;
        }

        /* Timeline Styles */
        .timeline {
            display: flex;
            flex-direction: column;
            margin: 20px auto;
            position: relative;
        }

        .timeline__event {
            margin-bottom: 20px;
            position: relative;
            display: flex;
            margin: 20px 0;
            border-radius: 6px;
            align-self: center;
            width: 100%;
            max-width: 850px;
        }

        .timeline__event:nth-child(2n + 1) {
            flex-direction: row-reverse;
        }

        .timeline__event:nth-child(2n + 1) .timeline__event__date {
            border-radius: 0 6px 6px 0;
        }

        .timeline__event:nth-child(2n + 1) .timeline__event__content {
            border-radius: 6px 0 0 6px;
        }

        .timeline__event:nth-child(2n + 1) .timeline__event__icon:before {
            content: "";
            width: 2px;
            height: 100%;
            position: absolute;
            top: 0%;
            left: 50%;
            right: auto;
            z-index: -1;
            transform: translateX(-50%);
            animation: fillTop 2s forwards 0.4s ease-in-out;
        }

        .timeline__event:nth-child(2n + 1) .timeline__event__icon:after {
            content: "";
            width: 100%;
            height: 2px;
            position: absolute;
            right: 0;
            z-index: -1;
            top: 50%;
            left: auto;
            transform: translateY(-50%);
            animation: fillLeft 2s forwards 0.4s ease-in-out;
        }

        .timeline__event__title {
            font-size: 1.2rem;
            line-height: 1.4;
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 1.5px;
        }

        .timeline__event__content {
            padding: 20px;
            box-shadow: 0 30px 60px -12px rgba(50, 50, 93, 0.25),
                0 18px 36px -18px rgba(0, 0, 0, 0.3),
                0 -12px 36px -8px rgba(0, 0, 0, 0.025);
            background: var(--background-color);
            width: calc(40vw - 84px);
            max-width: 640px;
            border-radius: 0 6px 6px 0;
        }

        .timeline__event__date {
            font-size: 1.3rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            padding: 0 20px;
            border-radius: 6px 0 0 6px;
        }

        .timeline__event__icon {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            align-self: center;
            margin: 0 20px;
            border-radius: 100%;
            width: 40px;
            box-shadow: 0 30px 60px -12px rgba(50, 50, 93, 0.25),
                0 18px 36px -18px rgba(0, 0, 0, 0.3),
                0 -12px 36px -8px rgba(0, 0, 0, 0.025);
            padding: 40px;
            height: 40px;
            position: relative;
        }

        .timeline__event__icon i {
            font-size: 32px;
        }

        .timeline__event__icon:before {
            content: "";
            width: 2px;
            height: 100%;
            position: absolute;
            top: 0%;
            z-index: -1;
            left: 50%;
            transform: translateX(-50%);
            animation: fillTop 2s forwards 0.4s ease-in-out;
        }

        .timeline__event__icon:after {
            content: "";
            width: 100%;
            height: 2px;
            position: absolute;
            left: 0%;
            z-index: -1;
            top: 50%;
            transform: translateY(-50%);
            animation: fillLeftOdd 2s forwards 0.4s ease-in-out;
        }

        .timeline__event__description {
            flex-basis: 60%;
        }

        .timeline__event:last-child .timeline__event__icon:before {
            content: none;
        }

        /* Type 1 - Primary */
        .timeline__event--type1 .timeline__event__date {
            color: #f6a4ec;
            background: #9251ac;
        }

        .timeline__event--type1 .timeline__event__icon {
            background: #f6a4ec;
            color: #9251ac;
        }

        .timeline__event--type1 .timeline__event__icon:before,
        .timeline__event--type1 .timeline__event__icon:after {
            background: #f6a4ec;
        }

        .timeline__event--type1 .timeline__event__title {
            color: #9251ac;
        }

        /* Type 2 - Secondary */
        .timeline__event--type2 .timeline__event__date {
            color: #87bbfe;
            background: #555ac0;
        }

        .timeline__event--type2 .timeline__event__icon {
            background: #87bbfe;
            color: #555ac0;
        }

        .timeline__event--type2 .timeline__event__icon:before,
        .timeline__event--type2 .timeline__event__icon:after {
            background: #87bbfe;
        }

        .timeline__event--type2 .timeline__event__title {
            color: #555ac0;
        }

        /* Type 3 - Tertiary */
        .timeline__event--type3 .timeline__event__date {
            color: #aff1b6;
            background-color: #24b47e;
        }

        .timeline__event--type3 .timeline__event__icon {
            background: #aff1b6;
            color: #24b47e;
        }

        .timeline__event--type3 .timeline__event__icon:before,
        .timeline__event--type3 .timeline__event__icon:after {
            background: #aff1b6;
        }

        .timeline__event--type3 .timeline__event__title {
            color: #24b47e;
        }



        /* Markdown content styling */
        #venture-detail-content {
        font-family: Roboto, sans-serif;
        line-height: 1.6;
        color: var(--text-color);
        }

        /* Headers */
        #venture-detail-content h1 {
        font-family: Nexa, sans-serif;
        font-size: 2em;
        margin-bottom: 0.5em;
        }
        #venture-detail-content h2 {
        font-size: 1.5em;
        margin-top: 1em;
        margin-bottom: 0.5em;
        }

        /* Paragraphs */
        #venture-detail-content p {
        margin: 0.5em 0;
        }

        /* Lists */
        #venture-detail-content ul {
        margin: 0.5em 0 0.5em 1.5em;
        }

        /* Bold text */
        #venture-detail-content strong {
        font-weight: bold;
        }

        #venture-detail-content img {
        display: block;
        margin: 1.5em auto;
        }

        #venture-detail-content ul {
        list-style-type: disc;     /* Ensures bullets are shown */
        margin-left: 1.5em;        /* Indent the list */
        padding-left: 1em;         /* Optional extra spacing */
        }

        #venture-detail-content li {
        margin-bottom: 0.5em;      /* Space between list items */
        }
        #venture-detail-content ol {
        list-style-type: decimal;
        margin-left: 1.5em;
        padding-left: 1em;
        }

        #venture-detail-content ul li::marker {
        color: #007bff; /* Make bullets blue */
        font-size: 1.2em;
        }
        /* Animations */
        .animated {
            animation-duration: 1s;
            animation-fill-mode: both;
            opacity: 0;
        }

        .fadeInUp {
            animation-name: fadeInUp;
        }

        .delay-1s {
            animation-delay: 0.1s;
        }

        .delay-2s {
            animation-delay: 0.2s;
        }

        .delay-3s {
            animation-delay: 0.3s;
        }

        @keyframes fadeInUp {
            from {
                transform: translate3d(0, 40px, 0);
                opacity: 0;
            }
            to {
                transform: translate3d(0, 0, 0);
                opacity: 1;
            }
        }

        @keyframes fillLeft {
            100% {
                right: 100%;
            }
        }

        @keyframes fillTop {
            100% {
                top: 100%;
            }
        }

        @keyframes fillLeftOdd {
            100% {
                left: 100%;
            }
        }

        /* Responsive styles */
        @media (max-width: 786px) {
            .timeline__event {
                flex-direction: column;
                align-self: center;
            }

            .timeline__event__content {
                width: 100%;
                max-width: 100%;
            }

            .timeline__event__icon {
                border-radius: 6px 6px 0 0;
                width: 100%;
                margin: 0;
                box-shadow: none;
            }

            .timeline__event__icon:before,
            .timeline__event__icon:after {
                display: none;
            }

            .timeline__event__date {
                border-radius: 0;
                padding: 20px;
            }

            .timeline__event:nth-child(2n + 1) {
                flex-direction: column;
                align-self: center;
            }

            .timeline__event:nth-child(2n + 1) .timeline__event__date {
                border-radius: 0;
                padding: 20px;
            }

            .timeline__event:nth-child(2n + 1) .timeline__event__icon {
                border-radius: 6px 6px 0 0;
                margin: 0;
            }

            .timeline__event__content,
            .timeline__event:nth-child(2n+1) .timeline__event__content {
                border-radius: 0 0 6px 6px;
            }
        }

        /* Language Toggle Styles */
        .language-toggle-desktop {
            display: flex;
            align-items: center;
            margin-left: 1rem;
        }

        .language-toggle-mobile {
            display: none;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color, #e5e7eb);
        }

        .language-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.25rem 0.5rem;
            background: var(--background-color);
            transition: all 0.3s ease;
        }

        .lang-btn {
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            background: transparent;
            cursor: pointer;
            color: var(--text-color);
        }

        .lang-btn:hover {
            background-color: var(--primary-color-1);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        .lang-btn.active {
            background-color: var(--primary-color-1);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .lang-btn.inactive {
            color: var(--text-color-light);
            background: transparent;
        }

        .lang-btn.inactive:hover {
            background-color: var(--background-color-light);
            color: var(--text-color);
        }

        .lang-separator {
            color: var(--text-color-light);
            font-weight: 300;
            transition: color 0.3s ease;
        }

        /* Dark mode specific adjustments */
        [data-theme="dark"] .language-toggle {
            border-color: var(--border-color);
            background: var(--background-color);
        }

        [data-theme="dark"] .lang-btn {
            color: var(--text-color);
        }

        [data-theme="dark"] .lang-btn:hover {
            background-color: var(--primary-color-1);
            color: white;
        }

        [data-theme="dark"] .lang-btn.active {
            background-color: var(--primary-color-1);
            color: white;
        }

        [data-theme="dark"] .lang-btn.inactive {
            color: var(--text-color-light);
            background: transparent;
        }

        [data-theme="dark"] .lang-btn.inactive:hover {
            background-color: var(--background-color-light);
            color: var(--text-color);
        }

        [data-theme="dark"] .lang-separator {
            color: var(--text-color-light);
        }

        /* RTL Support */
        [dir="rtl"] {
            text-align: right;
        }

        /* Navigation RTL fixes */
        [dir="rtl"] .nav {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .nav-links {
            flex-direction: row-reverse;
            gap: 2rem;
        }

        [dir="rtl"] .dropdown-content {
            right: 0;
            left: auto;
        }

        /* Hero Section RTL */
        [dir="rtl"] .ventures-hero {
            text-align: right;
        }

        [dir="rtl"] .ventures-hero h1 {
            text-align: right;
        }

        [dir="rtl"] .ventures-hero p {
            text-align: right;
        }

        /* General text alignment for RTL */
        [dir="rtl"] .section-title,
        [dir="rtl"] .section-subtitle {
            text-align: center;
        }

        [dir="rtl"] p {
            text-align: right;
        }

        [dir="rtl"] .text-center {
            text-align: center !important;
        }

        /* Timeline RTL fixes */
        [dir="rtl"] .timeline__event {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .timeline__event:nth-child(2n + 1) {
            flex-direction: row;
        }

        [dir="rtl"] .timeline__event__content {
            text-align: right;
        }

        [dir="rtl"] .timeline__event__title {
            text-align: right;
        }

        [dir="rtl"] .timeline__event__description {
            text-align: right;
        }

        /* Mobile Navigation Responsive Styles */
        @media (max-width: 768px) {
            .language-toggle-desktop {
                display: none;
            }

            .language-toggle-mobile {
                display: flex;
            }

            /* RTL Mobile Fixes */
            [dir="rtl"] .nav-links {
                text-align: right;
            }

            [dir="rtl"] .nav-link {
                text-align: right;
            }

            [dir="rtl"] .dropdown-link {
                text-align: right;
            }
        }

        /* Arabic Font Support */
        .lang-ar {
            font-family: 'Cairo', sans-serif;
        }

        .lang-en {
            font-family: 'Inter', sans-serif;
        }
    </style>
    <!-- MailerLite Universal -->
    <script>
        (function(w, d, e, u, f, l, n) {
            w[f] = w[f] || function() {
                    (w[f].q = w[f].q || [])
                    .push(arguments);
                }, l = d.createElement(e), l.async = 1, l.src = u,
                n = d.getElementsByTagName(e)[0], n.parentNode.insertBefore(l, n);
        })
        (window, document, 'script', 'https://assets.mailerlite.com/js/universal.js', 'ml');
        ml('account', '1446707');
    </script>
    <!-- End MailerLite Universal -->
</head>

<body class="bg-white">
    <!-- Header & Navigation -->
     <header class="header">
    <div class="container">
      <nav class="nav">
        <a href="../index.html" class="nav-logo">
          <img src="../assets/images/Main Horizontal Version.svg" alt="Sahla Logo" class="logo-img" id="nav-logo">
        </a>
        <input type="checkbox" id="mobile-menu-toggle" class="mobile-menu-toggle">
        <label for="mobile-menu-toggle" class="mobile-menu-button">
          <span></span>
          <span></span>
          <span></span>
        </label>
        <div class="nav-links">
          <div class="dropdown">
            <a href="#services" class="nav-link" data-i18n="nav.services">Services</a>
            <div class="dropdown-content">
              <a href="./process-automation.html" class="dropdown-link" data-i18n="nav.processAutomation">Process Automation</a>
              <a href="./technology-consulting.html" class="dropdown-link" data-i18n="nav.technologyConsulting">Technology Consulting</a>
            </div>
          </div>
          <a href="./ventures.html" class="nav-link" data-i18n="nav.ventures">Our Ventures</a>
          <a href="./partners.html" class="nav-link" data-i18n="nav.partners">Partners</a>
          <a href="./careers.html" class="nav-link" data-i18n="nav.careers">Careers</a>
          <a href="./blog.html" class="nav-link" data-i18n="nav.blog">Blog</a>
          <a href="./about.html" class="nav-link" data-i18n="nav.about">About us</a>
          <div class="px-4">
            <div class="h-6 border-l-2 border-gray-300"></div>
          </div>
          <a href="./contact.html" class="px-6 py-2 !rounded-button hover:bg-opacity-90 transition-all" style="background-color: var(--background-color-light); color: var(--text-color);" data-i18n="nav.contact">Contact Sales</a>

          <!-- Language Toggle - Mobile -->
          <div class="language-toggle-mobile">
            <div class="language-toggle">
              <button id="lang-en-mobile" class="lang-btn" data-lang="en">EN</button>
              <span class="lang-separator">|</span>
              <button id="lang-ar-mobile" class="lang-btn" data-lang="ar">العربية</button>
            </div>
          </div>
          <!-- Language Toggle - Desktop -->
          <div class="language-toggle-desktop">
            <div class="language-toggle">
              <button id="lang-en" class="lang-btn" data-lang="en">EN</button>
              <span class="lang-separator">|</span>
              <button id="lang-ar" class="lang-btn" data-lang="ar">العربية</button>
            </div>
          </div>

          <!-- Theme Switch -->
          <div class="theme-switch" id="theme-switch">
            <i class="ri-moon-line moon-icon"></i>
            <i class="ri-sun-line sun-icon"></i>
            <script>
              (() => {
                const savedTheme = localStorage.getItem('theme');
                const preferred = savedTheme || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                document.documentElement.setAttribute('data-theme', preferred);
              })();
            </script>
          </div>
        </div>
      </nav>
    </div>
  </header>

    <!-- 1. Hero Section -->
    <section class="ventures-hero pt-32 pb-16">
        <div class="container start px-4 text-start" style="margin-top: 10%;">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-6" data-i18n="venturesPage.hero.title">Building the Future, One Venture at a Time</h1>
            <p class="text-xl text-white max-w-2xl" data-i18n="venturesPage.hero.subtitle">From AI-powered platforms to immersive control systems and local commerce networks — explore the startups we've launched and the ones in the making.</p>
        </div>
    </section>

    <!-- 2. Venture Highlights Section -->
    <section class="section bg-light">
        <div class="container">
            <h2 class="section-title" data-i18n="venturesPage.highlights.title">Venture Highlights</h2>
            <p class="section-subtitle" data-i18n="venturesPage.highlights.subtitle">Current & Active Ventures</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12" id="ventures-container">
                <!-- Venture cards will be populated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- 3. Upcoming Ventures Section -->
    <section class="section">
        <div class="container">
            <h2 class="section-title" data-i18n="venturesPage.upcoming.title">Upcoming & Incubation Pipeline</h2>
            <p class="section-subtitle" data-i18n="venturesPage.upcoming.subtitle">A glimpse at what's cooking in the lab — our upcoming projects and exploratory domains.</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12" id="upcoming-ventures-container">
                <!-- Upcoming ventures will be populated by JavaScript -->
            </div>
        </div>
    </section>

    <!-- 4. Operating Model Section -->
    <section class="section bg-light">
        <div class="container">
            <h2 class="section-title" data-i18n="venturesPage.operatingModel.title">Operating Model</h2>
            <p class="section-subtitle" data-i18n="venturesPage.operatingModel.subtitle">How We Launch Ventures</p>

            <div class="timeline">
                <div class="timeline__event animated fadeInUp delay-3s timeline__event--type1">
                    <div class="timeline__event__icon">
                        <i class="ri-search-line"></i>
                    </div>
                    <div class="timeline__event__date" data-i18n="venturesPage.operatingModel.phase1.title">
                        Phase 1
                    </div>
                    <div class="timeline__event__content">
                        <div class="timeline__event__title" data-i18n="venturesPage.operatingModel.phase1.heading">
                            Identify Market Gaps
                        </div>
                        <div class="timeline__event__description">
                            <p data-i18n="venturesPage.operatingModel.phase1.description">We conduct thorough market research to identify underserved segments and unaddressed pain points. This phase includes competitor analysis, customer interviews, and trend forecasting to ensure our ventures solve real problems with significant market potential.</p>
                        </div>
                    </div>
                </div>

                <div class="timeline__event animated fadeInUp delay-2s timeline__event--type2">
                    <div class="timeline__event__icon">
                        <i class="ri-code-box-line"></i>
                    </div>
                    <div class="timeline__event__date" data-i18n="venturesPage.operatingModel.phase2.title">
                        Phase 2
                    </div>
                    <div class="timeline__event__content">
                        <div class="timeline__event__title" data-i18n="venturesPage.operatingModel.phase2.heading">
                            Prototype + Test Systems
                        </div>
                        <div class="timeline__event__description">
                            <p data-i18n="venturesPage.operatingModel.phase2.description">We build rapid prototypes to validate our hypotheses with real users. This allows us to test core assumptions, gather valuable feedback, and refine the product concept before investing heavily in development. Our iterative approach minimizes risk and maximizes learning.</p>
                        </div>
                    </div>
                </div>

                <div class="timeline__event animated fadeInUp delay-1s timeline__event--type3">
                    <div class="timeline__event__icon">
                        <i class="ri-rocket-line"></i>
                    </div>
                    <div class="timeline__event__date" data-i18n="venturesPage.operatingModel.phase3.title">
                        Phase 3
                    </div>
                    <div class="timeline__event__content">
                        <div class="timeline__event__title" data-i18n="venturesPage.operatingModel.phase3.heading">
                            Deploy MVP with Standards
                        </div>
                        <div class="timeline__event__description">
                            <p data-i18n="venturesPage.operatingModel.phase3.description">We develop minimum viable products with high standards for security, scalability, and user experience. Our engineering team ensures the foundation is solid, allowing the product to grow and evolve while maintaining performance and reliability as user adoption increases.</p>
                        </div>
                    </div>
                </div>

                <div class="timeline__event animated fadeInUp timeline__event--type1">
                    <div class="timeline__event__icon">
                        <i class="ri-expand-alt-line"></i>
                    </div>
                    <div class="timeline__event__date" data-i18n="venturesPage.operatingModel.phase4.title">
                        Phase 4
                    </div>
                    <div class="timeline__event__content">
                        <div class="timeline__event__title" data-i18n="venturesPage.operatingModel.phase4.heading">
                            Spin-Off or Partner for Scale
                        </div>
                        <div class="timeline__event__description">
                            <p data-i18n="venturesPage.operatingModel.phase4.description">Once a venture demonstrates traction, we either create an independent company with its own leadership team or partner with established organizations to scale effectively. This phase includes securing additional funding, expanding the team, and implementing growth strategies.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-8 text-center">
                <p class="text-lg font-medium text-primary !text-center" data-i18n="venturesPage.operatingModel.tagline">Every project starts as a system before it becomes a brand.</p>
            </div>
        </div>
    </section>

    <!-- 5. Collaborate Section -->
    <section class="section bg-primary py-20">
        <div class="container text-center">
            <h2 class="text-3xl font-bold text-white mb-6 !text-center" data-i18n="venturesPage.collaborate.title">Collaborate With Us</h2>
            <p class="text-xl text-white max-w-3xl mx-auto mb-8 !text-center" data-i18n="venturesPage.collaborate.description">We collaborate with developers, investors, and visionary organizations to co-launch ventures with real-world impact.</p>

            <a href="contact.html?type=collaboration" class="inline-block bg-white text-primary hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-300" data-i18n="venturesPage.collaborate.cta">
                Propose a Collaboration
            </a>
        </div>
    </section>

    <!-- Venture Detail Modal -->
    <div id="venture-modal" class="modal">
        <div class="modal-content">
            <span class="close-modal">&times;</span>
            <div id="venture-detail-content"></div>
        </div>
    </div>

<!-- f -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Company Info Column -->
                <div class="footer-info">
                    <a href="../index.html" class="footer-logo">
                        <img src="../assets/images/Negative Horizontal Version.svg" alt="Sahla Smart Solutions" class="logo-img" id="foot-logo">
                    </a>
                    <p class="footer-description" data-i18n="footer.description">Empowering businesses through innovative venture building and automation solutions in the MENA region.</p>
                    <div class="footer-social">
                        <a href="https://eg.linkedin.com/company/sahla-solutions" target="_blank" rel="noopener noreferrer"><i class="ri-linkedin-fill"></i></a>
                        <a href="https://x.com/sahlasolutions" target="_blank" rel="noopener noreferrer"><i class="ri-twitter-fill"></i></a>
                        <a href="https://www.facebook.com/SahlaSmartSolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-facebook-fill"></i></a>
                        <a href="https://www.instagram.com/sahlasolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-instagram-fill"></i></a>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div class="footer-links-column">
                    <h4 data-i18n="footer.quickLinks">Quick Links</h4>
                    <ul class="footer-links-list">
                        <li><a href="../index.html" data-i18n="footer.home">Home</a></li>
                        <li><a href="./about.html" data-i18n="footer.aboutUs">About Us</a></li>
                        <li><a href="./ventures.html" data-i18n="footer.ourVentures">Our Ventures</a></li>
                        <li><a href="./partners.html" data-i18n="nav.partners">Partners</a></li>
                        <li><a href="./careers.html" data-i18n="nav.careers">Careers</a></li>
                        <li><a href="./contact.html" data-i18n="footer.contactUs">Contact Us</a></li>
                    </ul>
                </div>

                <!-- Services Column -->
                <div class="footer-links-column">
                    <h4 data-i18n="footer.services">Services</h4>
                    <ul class="footer-links-list">
                        <li><a href="./process-automation.html" data-i18n="nav.processAutomation">Process Automation</a></li>
                        <li><a href="./technology-consulting.html" data-i18n="nav.technologyConsulting">Technology Consulting</a></li>
                    </ul>
                </div>

                <!-- Newsletter Column -->
                <div class="footer-newsletter">
                    <h4 data-i18n="footer.newsletter">Newsletter</h4>

                    <form class="mb-4">
                        <div class="flex">
                            <!-- <input type="email" placeholder="Your email" class="w-full px-4 py-2 bg-gray-800 border-none text-white text-sm rounded-l"> -->
                            <a href="javascript:void(0)" class="ml-onclick-form" onclick="ml('show', 'gturk0', true)" style="
                                display: inline-block;
                                padding: 8px 16px;
                                background-color: var(--primary-color-1);
                                color: white;
                                font-size: 16px;
                                font-family: sans-serif;
                                border: none;
                                border-radius: 6px;
                                text-decoration: none;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                                " onmouseover="this.style.backgroundColor='var(--primary-color-2)'" onmouseout="this.style.backgroundColor= 'var(--primary-color-3)'" data-i18n="footer.subscribeNewsletter">
                                Subscribe to Newsletter
                                </a>
                        </div>
                    </form>
                    <p data-i18n="footer.newsletterDescription">Subscribe to our newsletter to stay updated with our latest news and insights.</p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0" data-i18n="footer.copyright">© 2025 Sahla Smart Solutions. All rights reserved.</p>
                    <div class="flex space-x-6">
                        <a href="./pages/privacy-policy.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.privacyPolicy">Privacy Policy</a>
                        <a href="./pages/terms-of-service.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.termsOfService">Terms of Service</a>
                        <a href="./pages/cookies-policy.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.cookiesPolicy">Cookies Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Theme switch functionality
        const themeSwitch = document.getElementById('theme-switch');
        const html = document.documentElement;
        const navLogo = document.getElementById('nav-logo');
        const footLogo = document.getElementById('foot-logo');

        // Function to set and save theme
        const setTheme = (theme) => {
            html.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            themeSwitch.classList.toggle('dark', theme === 'dark');

            // Update logo based on theme
            if (theme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        };

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navLinks = document.querySelector('.nav-links');

        mobileMenuToggle.addEventListener('change', function() {
            if (this.checked) {
                navLinks.style.display = 'flex';
            } else {
                navLinks.style.display = '';
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                if (targetId === '#') return;

                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    const headerHeight = 80; // Approximate header height
                    const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });

                    // Close mobile menu if open
                    if (mobileMenuToggle.checked) {
                        mobileMenuToggle.checked = false;
                        navLinks.style.display = '';
                    }
                }
            });
        });

        // Sticky header effect
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 50) {
                header.classList.add('shadow-md');
            } else {
                header.classList.remove('shadow-md');
            }
        });

        // Set the correct theme class on icon container and logo after load
        document.addEventListener('DOMContentLoaded', () => {
            const currentTheme = html.getAttribute('data-theme');
            themeSwitch.classList.toggle('dark', currentTheme === 'dark');

            // Set initial logo based on theme
            if (currentTheme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }

            // Initialize ventures
            renderCurrentVentures();
            renderUpcomingVentures();
            setupModal();
        });

        // Toggle theme on click
        themeSwitch.addEventListener('click', () => {
            const isCurrentlyDark = html.getAttribute('data-theme') === 'dark';
            const newTheme = isCurrentlyDark ? 'light' : 'dark';
            setTheme(newTheme);
        });

        // Render current ventures
        function renderCurrentVentures() {
            const venturesContainer = document.getElementById('ventures-container');
            venturesContainer.innerHTML = '';

            venturesData.ventures.forEach(venture => {
                        let statusClass = 'status-planning';
                        if (venture.status.includes('Development')) {
                            statusClass = 'status-development';
                        } else if (venture.status.includes('Operational')) {
                            statusClass = 'status-operational';
                        }

                        // Determine which feature list to use (features, modules, or components)
                        const featureList = venture.features || venture.modules || venture.components || [];

                        const ventureElement = document.createElement('div');
                        ventureElement.className = 'venture-card shadow-md';
                        ventureElement.innerHTML = `
                    <div class="p-6">
                        <div class="flex justify-between items-start mb-4">
                            <h3 class="text-xl font-semibold">${venture.title}</h3>
                            <span class="status-badge ${statusClass}">${venture.status}</span>
                        </div>
                        <p class="text-gray-400 mb-4">${venture.overview}</p>
                        <div class="mb-4">
                            <h4 class="font-medium mb-2">Core Features:</h4>
                            <ul class="space-y-1">
                                ${featureList.map(feature => `
                                    <li class="flex items-start">
                                        <span class="text-primary mr-2"><i class="ri-check-line"></i></span>
                                        <span class="text-gray-400">${feature}</span>
                                    </li>
                                `).join('')}
                            </ul>
                        </div>
                        ${venture.vision ? `
                            <div class="mb-4">
                                <h4 class="font-medium mb-1">Vision:</h4>
                                <p class="text-gray-400">${venture.vision}</p>
                            </div>
                        ` : ''}
                        ${venture.target_audience ? `
                            <div class="mb-4">
                                <h4 class="font-medium mb-1">Target Audience:</h4>
                                <p class="text-gray-400">${venture.target_audience}</p>
                            </div>
                        ` : ''}
                        ${venture.phase ? `
                            <div class="mb-4">
                                <h4 class="font-medium mb-1">Phase:</h4>
                                <p class="text-gray-400">${venture.phase}</p>
                            </div>
                        ` : ''}
                        <button class="mt-4 text-primary font-medium hover:underline view-venture-details" data-venture-id="${venture.id}">
                            View Details
                        </button>
                    </div>
                `;

                venturesContainer.appendChild(ventureElement);
            });

            // Add event listeners to view details buttons
            document.querySelectorAll('.view-venture-details').forEach(button => {
                button.addEventListener('click', function() {
                    const ventureId = this.getAttribute('data-venture-id');
                    const venture = venturesData.ventures.find(v => v.id === ventureId);
                    showVentureDetails(venture);
                });
            });
        }

        // Render upcoming ventures
        function renderUpcomingVentures() {
            const upcomingContainer = document.getElementById('upcoming-ventures-container');
            upcomingContainer.innerHTML = '';

            venturesData.upcoming.forEach(venture => {
                const ventureElement = document.createElement('div');
                ventureElement.className = 'upcoming-venture';
                ventureElement.innerHTML = `
                    <div class="flex items-start !flex-row">
                        <div class="text-primary text-2xl mr-4">
                            <i class="ri-lightbulb-flash-line space-x-2"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold mb-2">${venture.title}</h3>
                            ${venture.planned_date ? `<p class="text-sm text-primary mb-2">Planned for ${venture.planned_date}</p>` : ''}
                            ${venture.stage ? `<p class="text-sm text-primary mb-2">${venture.stage}</p>` : ''}
                            <p class="text-gray-400">${venture.description}</p>
                        </div>
                    </div>
                `;

                upcomingContainer.appendChild(ventureElement);
            });
        }

        // Setup modal functionality
        function setupModal() {
            const modal = document.getElementById('venture-modal');
            const closeBtn = document.querySelector('.close-modal');

            // Close modal when clicking the close button
            closeBtn.addEventListener('click', () => {
                modal.classList.remove('active');
                document.body.style.overflow = '';
            });

            // Close modal when clicking outside the content
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    modal.classList.remove('active');
                    document.body.style.overflow = '';
                }
            });
        }

        // Show venture details in modal
        function showVentureDetails(venture) {
            const modal = document.getElementById('venture-modal');
            const contentContainer = document.getElementById('venture-detail-content');

            // Render markdown content
            contentContainer.innerHTML = marked.parse(venture.content);

            // Show modal
            modal.classList.add('active');
            document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open
        }
    </script>

    <!-- i18n Script -->
    <script src="../assets/js/i18n.js"></script>
</body>

</html>