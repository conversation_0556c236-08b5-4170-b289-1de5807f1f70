const partnersData = {
    categories: [
        { id: 'all', name: 'All Partners' },
        { id: 'financial', name: 'Financial Institutions' },
        { id: 'technology', name: 'Technology' },
        { id: 'government', name: 'Government' },
        { id: 'education', name: 'Education' }
    ],
    partners: [{
            name: 'OpenAI',
            icon: 'ri-openai-fill',
            category: 'technology',
            url: '#'
        },
        {
            name: 'Emirates NBD',
            icon: 'ri-bank-fill',
            category: 'financial',
            url: '#'
        },
        {
            name: 'Commercial International Bank (CIB)',
            icon: 'ri-bank-fill',
            category: 'financial',
            url: '#'
        },
        {
            name: 'Microsoft',
            icon: 'ri-microsoft-fill',
            category: 'technology',
            url: '#'
        },
        {
            name: 'MEA Egypt',
            icon: 'ri-government-fill',
            category: 'government',
            url: '#'
        },
        {
            name: 'Sharjah Publishing City',
            icon: 'ri-building-4-fill',
            category: 'government',
            url: '#'
        },
        {
            name: 'Amazon Web Services',
            icon: 'ri-amazon-fill',
            category: 'technology',
            url: '#'
        },
        {
            name: 'Notion',
            icon: 'ri-notion-fill',
            category: 'technology',
            url: '#'
        },
        {
            name: 'Google',
            icon: 'ri-google-fill',
            category: 'technology',
            url: '#'
        },
        {
            name: 'Discord',
            icon: 'ri-discord-fill',
            category: 'technology',
            url: '#'
        },
        {
            name: 'LinkedIn Marketing Solutions',
            icon: 'ri-linkedin-fill',
            category: 'technology',
            url: '#'
        },
        {
            name: 'MailChimp',
            icon: 'ri-mail-fill',
            category: 'technology',
            url: '#'
        },
        {
            name: 'Arduino',
            icon: 'ri-smartphone-fill',
            category: 'technology',
            url: '#'
        },
        {
            name: 'Raspberry Pi Foundation',
            icon: 'ri-smartphone-fill',
            category: 'technology',
            url: '#'
        },
        {
            name: 'LangChain',
            icon: 'ri-language-fill',
            category: 'technology',
            url: '#'
        },
        {
            name: 'Airtable',
            icon: 'ri-airtable-fill',
            category: 'technology',
            url: '#'
        },
        {
            name: 'Supabase',
            icon: 'ri-macbook-fill',
            category: 'technology',
            url: '#'
        },
        {
            name: 'Visa',
            icon: 'ri-visa-fill',
            category: 'financial',
            url: '#'
        }
    ]
};