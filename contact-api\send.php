<?php
// Set a timeout for the script
set_time_limit(60);

// Basic error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

// Log the start of the script
error_log("<PERSON><PERSON><PERSON> started: " . date('Y-m-d H:i:s'));

// Set headers to allow CORS and JSON responses
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Function to send JSON response
function sendJsonResponse($success, $message, $errors = [], $code = 200) {
    http_response_code($code);
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'errors' => $errors
    ]);
    exit;
}

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJsonResponse(false, 'Method not allowed', ['Only POST requests are allowed'], 405);
}

// Log request method and content type
error_log("Request method: " . $_SERVER['REQUEST_METHOD']);
error_log("Content-Type: " . (isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : 'Not set'));

// Function to sanitize input
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

// Function to validate email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL);
}

// Function to validate file upload
function isValidFile($file) {
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    $maxFileSize = 10 * 1024 * 1024; // 10MB
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return false;
    }
    
    if ($file['size'] > $maxFileSize) {
        return false;
    }
    
    // Get file extension
    $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    // Map extensions to MIME types
    $extensionToMime = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif',
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
    
    // Check if the file extension is in our allowed list
    if (!isset($extensionToMime[$fileExtension])) {
        return false;
    }
    
    // Check if the MIME type is in our allowed list
    $mimeType = $extensionToMime[$fileExtension];
    return in_array($mimeType, $allowedTypes);
}

// Initialize response array
$response = ['success' => false, 'message' => '', 'errors' => []];

// Check if the request is multipart/form-data (for file uploads)
if (isset($_SERVER['CONTENT_TYPE']) && strpos($_SERVER['CONTENT_TYPE'], 'multipart/form-data') !== false) {
    // Handle multipart/form-data
    error_log("Processing multipart/form-data request");
    $inputData = $_POST;
    $files = isset($_FILES) ? $_FILES : [];
    error_log("POST data: " . print_r($inputData, true));
    error_log("FILES data: " . print_r($files, true));
} else {
    // Handle JSON data
    error_log("Processing JSON request");
    $inputData = json_decode(file_get_contents("php://input"), true);
    $files = [];
    error_log("JSON data: " . print_r($inputData, true));
}

// Check if the necessary data is provided
if (empty($inputData)) {
    error_log("No input data provided");
    sendJsonResponse(false, 'Invalid data received', ['No data provided'], 400);
}

// Validate required fields
$requiredFields = ['full-name', 'email'];
foreach ($requiredFields as $field) {
    if (empty($inputData[$field])) {
        $response['errors'][] = "Missing required field: $field";
    }
}

// Validate email
if (!empty($inputData['email']) && !isValidEmail($inputData['email'])) {
    $response['errors'][] = 'Invalid email format';
}

// If there are validation errors, return them
if (!empty($response['errors'])) {
    error_log("Validation errors: " . print_r($response['errors'], true));
    sendJsonResponse(false, 'Validation failed', $response['errors'], 400);
}

// Determine the recipient email based on the form type
$recipientEmail = '<EMAIL>'; // Default fallback

// Check if this is a career application
if (isset($inputData['form-type']) && $inputData['form-type'] === 'career') {
    $recipientEmail = '<EMAIL>';
    error_log("Career application detected, sending to: " . $recipientEmail);
} elseif (isset($inputData['recipient']) && isValidEmail($inputData['recipient'])) {
    // Use the recipient specified in the form data if it's a valid email
    $recipientEmail = $inputData['recipient'];
    error_log("Using recipient from form data: " . $recipientEmail);
} elseif (getenv('DEFAULT_RECIPIENT') && isValidEmail(getenv('DEFAULT_RECIPIENT'))) {
    // Use the default recipient from environment variables if available
    $recipientEmail = getenv('DEFAULT_RECIPIENT');
    error_log("Using default recipient from env: " . $recipientEmail);
} else {
    error_log("Using fallback recipient: " . $recipientEmail);
}

// Create email content
$subject = isset($inputData['subject']) ? $inputData['subject'] : 'New Career Application';
$fromEmail = getenv('FROM_EMAIL') ?: $inputData['email'];
$fromName = getenv('FROM_NAME') ?: 'Sahla Solutions';

// Create HTML body
$htmlBody = "<h3>Career Application Information</h3>";

// Add standard fields if they exist
$standardFields = ['full-name', 'email', 'phone', 'linkedin', 'portfolio', 'cover-letter'];
foreach ($standardFields as $field) {
    if (isset($inputData[$field]) && !empty($inputData[$field])) {
        $displayName = ucfirst(str_replace('-', ' ', $field));
        $htmlBody .= "<p><strong>$displayName:</strong> " . sanitizeInput($inputData[$field]) . "</p>";
    }
}

// Dynamically add extra fields
foreach ($inputData as $key => $value) {
    if (!in_array($key, $standardFields) && $key !== 'form-type' && $key !== 'recipient') {
        $displayName = ucfirst(str_replace('-', ' ', $key));
        $htmlBody .= "<p><strong>$displayName:</strong> " . sanitizeInput($value) . "</p>";
    }
}

// Handle file attachments
$attachments = [];
if (!empty($files)) {
    error_log("Processing file attachments");
    $htmlBody .= "<h3>Attachments</h3>";
    $htmlBody .= "<ul>";
    
    foreach ($files as $fileKey => $fileData) {
        if (is_array($fileData['name'])) {
            // Multiple files with the same field name
            for ($i = 0; $i < count($fileData['name']); $i++) {
                if ($fileData['error'][$i] === UPLOAD_ERR_OK) {
                    $tmpName = $fileData['tmp_name'][$i];
                    $fileName = $fileData['name'][$i];
                    
                    error_log("File extension: " . pathinfo($fileName, PATHINFO_EXTENSION));
                    $isValid = isValidFile(['tmp_name' => $tmpName, 'name' => $fileName, 'size' => $fileData['size'][$i], 'error' => $fileData['error'][$i]]);
                    error_log("File validation result: " . ($isValid ? 'Valid' : 'Invalid'));
                    
                    if ($isValid) {
                        // Read file content and encode as base64
                        $fileContent = file_get_contents($tmpName);
                        $base64Content = base64_encode($fileContent);
                        
                        // Add to attachments array
                        $attachments[] = [
                            'filename' => $fileName,
                            'content' => $base64Content,
                            'encoding' => 'base64'
                        ];
                        
                        $htmlBody .= "<li>" . sanitizeInput($fileName) . "</li>";
                        error_log("File attached: " . $fileName);
                    } else {
                        $response['errors'][] = "Invalid file: $fileName";
                        error_log("Invalid file: " . $fileName);
                    }
                }
            }
        } else {
            // Single file
            if ($fileData['error'] === UPLOAD_ERR_OK) {
                error_log("File extension: " . pathinfo($fileData['name'], PATHINFO_EXTENSION));
                $isValid = isValidFile($fileData);
                error_log("File validation result: " . ($isValid ? 'Valid' : 'Invalid'));
                
                if ($isValid) {
                    // Read file content and encode as base64
                    $fileContent = file_get_contents($fileData['tmp_name']);
                    $base64Content = base64_encode($fileContent);
                    
                    // Add to attachments array
                    $attachments[] = [
                        'filename' => $fileData['name'],
                        'content' => $base64Content,
                        'encoding' => 'base64'
                    ];
                    
                    $htmlBody .= "<li>" . sanitizeInput($fileData['name']) . "</li>";
                    error_log("File attached: " . $fileData['name']);
                } else {
                    $response['errors'][] = "Invalid file: " . $fileData['name'];
                    error_log("Invalid file: " . $fileData['name']);
                }
            }
        }
    }
    
    $htmlBody .= "</ul>";
}

// Create plain text version
$textBody = strip_tags($htmlBody);

// Prepare data for the API
$apiData = [
    'to' => $recipientEmail,
    'subject' => $subject,
    'text' => $textBody,
    'html' => $htmlBody,
    'from' => $fromEmail,
    'fromName' => $fromName
];

// Add attachments if any
if (!empty($attachments)) {
    $apiData['attachments'] = $attachments;
}

error_log("Preparing to send email via API");
error_log("API Data: " . json_encode(array_merge(
    $apiData,
    ['attachments' => !empty($attachments) ? count($attachments) . ' attachments' : 'No attachments']
)));

// Send email via the Vercel serverless function
$apiUrl = 'https://serverless-nodemailer.vercel.app/api/send-email';

// Initialize cURL session
$ch = curl_init($apiUrl);

// Set cURL options
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($apiData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen(json_encode($apiData))
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

// Execute cURL request
error_log("Sending request to API");
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$curlError = curl_error($ch);

// Close cURL session
curl_close($ch);

// Log the response
error_log("API Response Code: " . $httpCode);
error_log("API Response: " . $response);
if (!empty($curlError)) {
    error_log("cURL Error: " . $curlError);
}

// Process the response
if ($httpCode >= 200 && $httpCode < 300) {
    // Success
    $responseData = json_decode($response, true);
    if (isset($responseData['success']) && $responseData['success']) {
        error_log("Email sent successfully via API");
        sendJsonResponse(true, 'Email sent successfully');
    } else {
        error_log("API returned success status code but indicated failure");
        sendJsonResponse(false, 'Email could not be sent', ['API returned an error'], 500);
    }
} else {
    // Error
    error_log("API request failed with status code: " . $httpCode);
    sendJsonResponse(false, 'Email could not be sent', ['API request failed with status code: ' . $httpCode], 500);
}

error_log("Script completed: " . date('Y-m-d H:i:s'));
?>
