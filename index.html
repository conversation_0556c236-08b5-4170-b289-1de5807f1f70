<!DOCTYPE html>
<html lang="en">

<head>
    <link rel="icon" type="image/x-icon" href="./assets/images/favicon.ico?v=2">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sahla Smart Solutions - Venture Builder & Automation Services</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0056b3',
                        secondary: '#4dabf7'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="./assets/css/style.css">
    <link rel="stylesheet" href="./assets/css/navigation.css">

    <!-- Dynamic Font Loading -->
    <link id="google-fonts" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- i18next Core and HTTP Backend -->
    <script src="https://unpkg.com/i18next@23.7.16/dist/umd/i18next.min.js"></script>
    <script src="https://unpkg.com/i18next-http-backend@2.4.2/i18nextHttpBackend.min.js"></script>

    <script src="./assets/js/partners-data.js"></script>
    <script src="./assets/js/success-stories-data.js?v=1.0.2"></script>
    <style>
         :where([class^="ri-"])::before {
            content: "\f3c2";
        }

        .hero-section {
            background-image: linear-gradient(to bottom, #111827d8, #22267a8f), url('https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg');
            background-size: cover;
            background-position: center;
        }

        .process-line {
            background: linear-gradient(90deg, var(--primary-color-1) 0%, var(--secondary-color-1) 100%);
            height: 2px;
            position: absolute;
            top: 3rem;
            left: 2.5rem;
            right: 2.5rem;
            z-index: 0;
        }

        .process-step {
            z-index: 1;
            position: relative;
        }

        .process-number {
            width: 3rem;
            height: 3rem;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: white;
            border: 2px solid var(--primary-color-1);
            color: var(--primary-color-1);
            font-weight: 700;
        }
        /* Partner section styles */

        .partners-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .partner-logo-card {
            aspect-ratio: 1;
            width: 100%;
            max-width: 200px;
            margin: 0 auto;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .partner-logo-card.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .partner-logo-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
            background: var(--bg-light);
            border-radius: 1rem;
            transition: all 0.3s ease;
        }

        .partner-logo-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .partner-icon-wrapper {
            width: 64px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 2rem;
            color: var(--primary-color);
        }

        .partner-name {
            text-align: center;
            font-weight: 500;
            color: var(--text-color);
        }

        .filter-btn {
            padding: 0.5rem 1.5rem;
            border-radius: 2rem;
            background: transparent;
            color: var(--primary-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn:hover {
            background: var(--primary-color);
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .filter-btn.active {
            background: var(--primary-color-1);
            color: white;
            border-color: var(--primary-color);
        }
        /* Sahla Edge Styles */

        .sahla-edge {
            margin-top: 3rem;
        }

        .edge-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .edge-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.5rem;
            background: var(--bg-light);
            border-radius: 1rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .edge-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .edge-icon {
            width: 48px;
            height: 48px;
            min-width: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            color: var(--primary-color-1);
            border-radius: 12px;
            font-size: 1.5rem;
        }

        .edge-content h4 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-color);
        }

        .edge-content p {
            font-size: 0.9rem;
            line-height: 1.5;
            color: var(--text-light);
            margin: 0;
        }

        .accent-line {
            height: 4px;
            width: 80px;
            background: var(--primary-color);
            margin-top: 1.5rem;
            border-radius: 2px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .feature-card {
            background: var(--bg-gradient-light);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border-bottom: 4px solid transparent;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            border-bottom: 4px solid var(--primary-color);
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1.5rem;
            display: inline-block;
            background: rgba(var(--primary-color-rgb), 0.1);
            padding: 1rem;
            border-radius: 50%;
        }

        .primary-color {
            color: var(--primary-color);
        }

        .pulse-animation {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(var(--primary-color-rgb), 0.4);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(var(--primary-color-rgb), 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(var(--primary-color-rgb), 0);
            }
        }

        .hover-lift {
            transition: transform 0.3s ease;
        }

        .hover-lift:hover {
            transform: translateY(-10px);
        }

        .feature-hover-content {
            opacity: 0;
            transition: opacity 0.3s ease;
            margin-top: 1rem;
        }

        .feature-card:hover .feature-hover-content {
            opacity: 1;
        }

        .btn-learn-more {
            display: inline-block;
            color: var(--primary-color);
            font-weight: 600;
            text-decoration: none;
            position: relative;
        }

        .btn-learn-more:after {
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            bottom: -4px;
            left: 0;
            background-color: var(--primary-color);
            transform: scaleX(0);
            transform-origin: bottom right;
            transition: transform 0.3s;
        }

        .btn-learn-more:hover:after {
            transform: scaleX(1);
            transform-origin: bottom left;
        }

        .reveal-animation {
            opacity: 0;
            transform: translateY(20px);
            animation: reveal 0.8s ease forwards;
        }

        @keyframes reveal {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--text-color);
            padding: 0.75rem 1.5rem;
            border-radius: 30px;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            border: 2px solid var(--primary-color);
        }

        .btn-primary:hover {
            background: transparent;
            color: var(--primary-color);
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(var(--primary-color-rgb), 0.2);
        }

        .btn-lg {
            padding: 1rem 2rem;
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .edge-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Language Toggle Styles */
        .language-toggle-desktop {
            display: flex;
            align-items: center;
            margin-left: 1rem;
        }

        .language-toggle-mobile {
            display: none;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color, #e5e7eb);
        }

        .language-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.25rem 0.5rem;
            background: var(--background-color);
            transition: all 0.3s ease;
        }

        .lang-btn {
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            background: transparent;
            cursor: pointer;
            color: var(--text-color);
        }

        .lang-btn:hover {
            background-color: var(--primary-color-1);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        .lang-btn.active {
            background-color: var(--primary-color-1);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .lang-btn.inactive {
            color: var(--text-color-light);
            background: transparent;
        }

        .lang-btn.inactive:hover {
            background-color: var(--background-color-light);
            color: var(--text-color);
        }

        .lang-separator {
            color: var(--text-color-light);
            font-weight: 300;
            transition: color 0.3s ease;
        }

        /* Dark mode specific adjustments */
        [data-theme="dark"] .language-toggle {
            border-color: var(--border-color);
            background: var(--background-color);
        }

        [data-theme="dark"] .lang-btn {
            color: var(--text-color);
        }

        [data-theme="dark"] .lang-btn:hover {
            background-color: var(--primary-color-1);
            color: white;
        }

        [data-theme="dark"] .lang-btn.active {
            background-color: var(--primary-color-1);
            color: white;
        }

        [data-theme="dark"] .lang-btn.inactive {
            color: var(--text-color-light);
            background: transparent;
        }

        [data-theme="dark"] .lang-btn.inactive:hover {
            background-color: var(--background-color-light);
            color: var(--text-color);
        }

        [data-theme="dark"] .lang-separator {
            color: var(--text-color-light);
        }

        /* Navigation CTA Button */
        .nav-cta-btn {
            background: var(--primary-color, #0056b3);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-cta-btn:hover {
            background-color: var(--primary-color-2, #004494);
            transform: translateY(-1px);
        }

        .nav-divider {
            display: flex;
            align-items: center;
            padding: 0 1rem;
        }

        /* RTL Support */
        [dir="rtl"] {
            text-align: right;
        }

        /* Navigation RTL fixes */
        [dir="rtl"] .nav {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .nav-links {
            flex-direction: row-reverse;
            gap: 2rem;
        }

        [dir="rtl"] .nav-logo {
            margin-left: 0;
            margin-right: 0;
        }

        [dir="rtl"] .nav-links .dropdown {
            text-align: right;
        }

        [dir="rtl"] .dropdown-content {
            left: auto;
            right: 0;
            text-align: right;
        }

        [dir="rtl"] .dropdown-link {
            text-align: right;
        }

        [dir="rtl"] .nav-divider {
            padding: 0 1rem;
        }

        [dir="rtl"] .language-toggle-desktop {
            margin-left: 0;
            margin-right: 1rem;
        }

        [dir="rtl"] .language-toggle {
            flex-direction: row-reverse;
        }

        /* Mobile RTL Navigation */
        [dir="rtl"] .mobile-menu-button {
            order: -1;
        }

        [dir="rtl"] .nav-links {
            text-align: right;
        }

        [dir="rtl"] .nav-link {
            text-align: right;
        }

        [dir="rtl"] .nav-cta-btn {
            text-align: center;
        }

        /* Hero buttons RTL */
        [dir="rtl"] .hero-section .flex {
            flex-direction: row-reverse;
            gap: 1rem;
        }

        [dir="rtl"] .hero-section .max-w-2xl {
            text-align: right;
        }

        [dir="rtl"] .hero-section h1 {
            text-align: right;
        }

        [dir="rtl"] .hero-section p {
            text-align: right;
        }

        /* About section RTL */
        [dir="rtl"] .about-content .flex {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .space-x-4 > * + * {
            margin-left: 0;
            margin-right: 1rem;
        }

        [dir="rtl"] .space-x-reverse > * + * {
            margin-left: 1rem;
            margin-right: 0;
        }

        [dir="rtl"] .text-left {
            text-align: right;
        }

        [dir="rtl"] .justify-start {
            justify-content: flex-end;
        }

        /* Hero Section RTL */
        [dir="rtl"] .hero-section {
            text-align: right;
        }

        [dir="rtl"] .hero-section .max-w-2xl {
            justify-self: end;
        }

        [dir="rtl"] .hero-section .flex {
            justify-content: flex-end;
        }

        /* About Section RTL */
        [dir="rtl"] .about-content {
            direction: rtl;
        }

        [dir="rtl"] .about-text h2 {
            text-align: right;
        }

        [dir="rtl"] .about-description {
            text-align: right;
        }


        [dir="rtl"] .edge-content {
            text-align: right;
        }

        /* Newsletter Section RTL */
        [dir="rtl"] .footer-newsletter .flex {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .footer-newsletter {
            text-align: right;
        }

        [dir="rtl"] .footer-newsletter .flex > * {
            margin-left: 0;
            margin-right: 0.5rem;
        }

        [dir="rtl"] .footer-newsletter .flex > *:first-child {
            margin-right: 0;
        }

        /* General text alignment for RTL */
        [dir="rtl"] .section-title,
        [dir="rtl"] .section-subtitle {
            text-align: center;
        }

        [dir="rtl"] p {
            text-align: right;
        }

        [dir="rtl"] .text-center {
            text-align: center !important;
        }

        /* Success Stories RTL */
        [dir="rtl"] .story-header {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .story-avatar {
            margin-right: 0;
            margin-left: 1rem;
        }

        [dir="rtl"] .story-info {
            text-align: right;
        }

        [dir="rtl"] .story-quote {
            text-align: right;
        }

        [dir="rtl"] .carousel-controls {
            flex-direction: row-reverse;
        }

        /* Pagination button spacing */
        .pagination-btn {
            margin: 0 0.75rem !important;
        }

        /* RTL pagination fixes */
        [dir="rtl"] .pagination-container {
            direction: rtl;
        }

        /* Features section RTL - move icons to the right */
        /* [dir="rtl"] .feature-card {
            text-align: right;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
        } */

        [dir="rtl"] .feature-icon {
            margin: 0 0 1.5rem 0 !important;
            align-self: anchor-center !important;
            order: -1; /* Move icon to top-right */
        }

        /* Ensure feature cards maintain proper RTL layout */
        /* [dir="rtl"] .features-grid .feature-card {
            direction: rtl;
        } */

        [dir="rtl"] .feature-card h3,
        [dir="rtl"] .feature-card p {
            text-align: right;
            direction: rtl;
        }


        [dir="rtl"] .edge-icon {
            margin: 0 auto 1rem auto; /* Center the icon */
            align-self: center; /* Ensure icon stays centered */
        }

        [dir="rtl"] .edge-content {
            text-align: right; /* Only text content is right-aligned */
            width: 100%; /* Full width for text content */
        }

        [dir="rtl"] .edge-content h4,
        [dir="rtl"] .edge-content p {
            text-align: right;
            direction: rtl;
        }

        /* About section RTL text alignment */
        [dir="rtl"] .about-text {
            text-align: right;
        }

        [dir="rtl"] .about-text h2 {
            text-align: right !important;
        }

        [dir="rtl"] .about-description {
            text-align: right;
        }

        [dir="rtl"] .sahla-edge h3 {
            text-align: right;
        }

        /* Footer RTL fixes */
        [dir="rtl"] .footer-content {
            direction: rtl;
        }

        [dir="rtl"] .footer-links-column h4 {
            text-align: right;
        }

        [dir="rtl"] .footer-links-list {
            text-align: right;
        }

        [dir="rtl"] .footer-newsletter h4 {
            text-align: right;
        }

        [dir="rtl"] .footer-newsletter p {
            text-align: right;
        }

        [dir="rtl"] .footer-newsletter .flex {
            justify-content: flex-end;
        }

        /* Copyright section RTL */
        [dir="rtl"] .border-t .flex {
            flex-direction: row-reverse;
        }

        /* Success Stories Carousel RTL fixes */
        [dir="rtl"] .carousel-controls {
            flex-direction: row-reverse;
        }



        /* Mobile Navigation Responsive Styles */
        @media (max-width: 768px) {
            .language-toggle-desktop {
                display: none;
            }

            .language-toggle-mobile {
                display: flex;
            }

            .nav-divider {
                display: none;
            }

            .nav-cta-btn {
                width: 100%;
                text-align: center;
                margin-top: 0.5rem;
            }

            /* RTL Mobile Fixes */
            [dir="rtl"] .nav-links {
                text-align: right;
            }

            [dir="rtl"] .nav-link {
                text-align: right;
            }

            [dir="rtl"] .dropdown-link {
                text-align: right;
            }

            [dir="rtl"] .nav-cta-btn {
                text-align: center;
            }
        }

        /* Arabic Font Support */
        .lang-ar {
            font-family: 'Cairo', sans-serif;
        }

        .lang-en {
            font-family: 'Inter', sans-serif;
        }
    </style>
    <!-- MailerLite Universal -->
    <script>
        (function(w, d, e, u, f, l, n) {
            w[f] = w[f] || function() {
                    (w[f].q = w[f].q || [])
                    .push(arguments);
                }, l = d.createElement(e), l.async = 1, l.src = u,
                n = d.getElementsByTagName(e)[0], n.parentNode.insertBefore(l, n);
        })
        (window, document, 'script', 'https://assets.mailerlite.com/js/universal.js', 'ml');
        ml('account', '1446707');
    </script>
    <!-- End MailerLite Universal -->
</head>

<body class="bg-white">
    <!-- Header & Navigation -->
    <header class="header">
        <div class="container">
            <nav class="nav">
                <a href="./index.html" class="nav-logo">
                    <img src="./assets/images/Main Horizontal Version.svg" alt="Sahla Logo" class="logo-img" id="nav-logo">
                </a>
                <input type="checkbox" id="mobile-menu-toggle" class="mobile-menu-toggle">
                <label for="mobile-menu-toggle" class="mobile-menu-button">
                    <span></span>
                    <span></span>
                    <span></span>
                </label>
                <div class="nav-links">
                    <div class="dropdown">
                        <a href="#services" class="nav-link" data-i18n="nav.services">Services</a>
                        <div class="dropdown-content">
                            <a href="./pages/process-automation.html" class="dropdown-link" data-i18n="nav.processAutomation">Process Automation</a>
                            <a href="./pages/technology-consulting.html" class="dropdown-link" data-i18n="nav.technologyConsulting">Technology Consulting</a>
                        </div>
                    </div>
                    <a href="./pages/ventures.html" class="nav-link" data-i18n="nav.ventures">Our Ventures</a>
                    <a href="./pages/partners.html" class="nav-link" data-i18n="nav.partners">Partners</a>
                    <a href="./pages/careers.html" class="nav-link" data-i18n="nav.careers">Careers</a>
                    <a href="./pages/blog.html" class="nav-link" data-i18n="nav.blog">Blog</a>
                    <a href="./pages/about.html" class="nav-link" data-i18n="nav.about">About us</a>
                    <div class="nav-divider">
                        <div class="h-6 border-l-2 border-gray-300"></div>
                    </div>
                    <a href="./pages/contact.html" class="nav-cta-btn" data-i18n="nav.contact">Contact Sales</a>

                    <!-- Language Toggle - Mobile -->
                    <div class="language-toggle-mobile">
                        <div class="language-toggle">
                            <button id="lang-en-mobile" class="lang-btn" data-lang="en">EN</button>
                            <span class="lang-separator">|</span>
                            <button id="lang-ar-mobile" class="lang-btn" data-lang="ar">العربية</button>
                        </div>
                    </div>
                    <!-- Language Toggle - Desktop -->
                    <div class="language-toggle-desktop">
                        <div class="language-toggle">
                            <button id="lang-en" class="lang-btn" data-lang="en">EN</button>
                            <span class="lang-separator">|</span>
                            <button id="lang-ar" class="lang-btn" data-lang="ar">العربية</button>
                        </div>
                    </div>

                    <!-- Theme Switch -->
                    <div class="theme-switch" id="theme-switch">
                        <i class="ri-moon-line moon-icon"></i>
                        <i class="ri-sun-line sun-icon"></i>
                        <script>
                            (() => {
                                const savedTheme = localStorage.getItem('theme');
                                const preferred = savedTheme || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                                document.documentElement.setAttribute('data-theme', preferred);
                            })();
                        </script>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-section min-h-screen flex justify-start pt-20">
        <div class="container mx-auto px-6 w-full">
            <div class="max-w-2xl">
                <h1 class="h1" data-i18n="hero.title">We Build Ventures.<br> We Automate Success.</h1>
                <p class="text-xl mb-8 p1 hero-text" data-i18n="hero.subtitle">We turn bold ideas into scalable ventures and supercharge businesses with AI-driven automation.</p>
                <div class="flex gap-4">
                    <a href="#contact" class="bg-white text-primary px-8 py-3 !rounded-button whitespace-nowrap font-medium hover:bg-opacity-90 transition duration-300" data-i18n="hero.cta1">Automate Your Business</a>
                    <a href="#process" class="bg-transparent border-2 border-white text-white px-8 py-3 !rounded-button whitespace-nowrap font-medium hover:bg-white hover:bg-opacity-10 transition duration-300" data-i18n="hero.cta2">Explore Our Ventures</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Services Section -->
    <section id="services" class="section bg-light">
        <div class="container">
            <h2 class="section-title" data-i18n="services.title">Our Services</h2>
            <p class="section-subtitle" data-i18n="services.subtitle">Not only are we a venture builder — we also provide comprehensive technology, venture, and automation services designed to help businesses grow, flourish, and achieve long-term sustainability</p>

            <div class="services-grid justify-center mx-auto px-6">
                <!-- Service 1 -->
                <!-- <div class="service-card">
                    <div class="service-icon">
                        <i class="ri-rocket-line"></i>
                    </div>
                    <h3 class="h3">Venture Building</h3>
                    <p>We transform promising ideas into successful businesses through our end-to-end venture building process.</p>
                    <a href="./pages/venture-building.html" class="service-link">Learn More <i class="ri-arrow-right-line"></i></a>
                </div> -->

                <!-- Service 2 -->
                <div class="service-card">
                    <div class="service-icon">
                        <i class="ri-settings-line"></i>
                    </div>
                    <h3 class="h3" data-i18n="services.processAutomation.title">Process Automation</h3>
                    <p data-i18n="services.processAutomation.description">Streamline your operations with custom automation solutions that increase efficiency and reduce costs.</p>
                    <a href="./pages/process-automation.html" class="service-link" data-i18n="services.learnMore">Learn More <i class="ri-arrow-right-line"></i></a>
                </div>

                <!-- Service 3 -->
                <!-- <div class="service-card">
                    <div class="service-icon">
                        <i class="ri-computer-line"></i>
                    </div>
                    <h3 class="h3">Digital Transformation</h3>
                    <p>We help established companies innovate and adapt to the digital landscape with custom solutions.</p>
                    <a href="./pages/digital-transformation.html" class="service-link">Learn More <i class="ri-arrow-right-line"></i></a>
                </div> -->

                <!-- Service 4 -->
                <div class="service-card">
                    <div class="service-icon">
                        <i class="ri-lightbulb-line"></i>
                    </div>
                    <h3 class="h3" data-i18n="services.technologyConsulting.title">Technology Consulting</h3>
                    <p data-i18n="services.technologyConsulting.description">Our experts provide strategic guidance to identify opportunities and develop innovation roadmaps.</p>
                    <a href="./pages/technology-consulting.html" class="service-link" data-i18n="services.learnMore">Learn More <i class="ri-arrow-right-line"></i></a>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Work With Us Section -->
    <section id="why-work-with-us" class="section bg-gradient-light">
        <div class="container">
            <div class="text-center mb-16 reveal-animation">
                <h2 class="section-title" data-i18n="whyWorkWithUs.title">Why Work With Us?</h2>
                <p class="section-subtitle" data-i18n="whyWorkWithUs.subtitle">We combine expertise, innovation, and a proven approach to deliver exceptional results.</p>
                <div class="accent-line mx-auto"></div>
            </div>

            <div class="features-grid">
                <div class="feature-card hover-lift">
                    <div class="feature-icon pulse-animation">
                        <i class="ri-robot-line primary-color"></i>
                    </div>
                    <h3 class="h3" data-i18n="whyWorkWithUs.aiPowered.title">AI-Powered Operations</h3>
                    <p data-i18n="whyWorkWithUs.aiPowered.description">Leverage cutting-edge artificial intelligence to automate complex processes and gain valuable insights from your data.</p>
                </div>

                <div class="feature-card hover-lift">
                    <div class="feature-icon pulse-animation">
                        <i class="ri-line-chart-line primary-color"></i>
                    </div>
                    <h3 class="h3" data-i18n="whyWorkWithUs.scalableResults.title">Practical, Scalable Results</h3>
                    <p data-i18n="whyWorkWithUs.scalableResults.description">Our solutions deliver immediate value while being designed to scale with your business as it grows.</p>
                </div>

                <div class="feature-card hover-lift">
                    <div class="feature-icon pulse-animation">
                        <i class="ri-team-line primary-color"></i>
                    </div>
                    <h3 class="h3" data-i18n="whyWorkWithUs.crossFunctional.title">Cross-functional Teams</h3>
                    <p data-i18n="whyWorkWithUs.crossFunctional.description">Work with diverse teams of experts across technology, business, and industry domains for comprehensive solutions.</p>

                </div>

                <div class="feature-card hover-lift">
                    <div class="feature-icon pulse-animation">
                        <i class="ri-settings-4-line primary-color"></i>
                    </div>
                    <h3 class="h3" data-i18n="whyWorkWithUs.agileFlexible.title">Agile & Flexible</h3>
                    <p data-i18n="whyWorkWithUs.agileFlexible.description">Our agile approach allows for rapid iterations and adjustments to ensure we meet your evolving needs.</p>

                </div>
            </div>

            <div class="text-center mt-12  ">
                <a href="./pages/contact.html" class=" cursor-pointer bg-[#0056b3] text-white  p-3 rounded-md hover:bg-[--primary-color-3]  transition-all duration-300 ease-in-out hover:-translate-y-1.5 inline-block delay-100 " data-i18n="whyWorkWithUs.cta">Start Your Journey With Us</a>
            </div>
        </div>
    </section>

    <!-- Success Stories Section -->
    <section id="success-stories" class="section bg-light">
        <div class="container">
            <div class="text-center mb-16">
                <h2 class="section-title" data-i18n="successStories.title">Success Stories</h2>
                <p class="section-subtitle" data-i18n="successStories.subtitle">Discover how we've helped businesses transform and grow through our services.</p>
            </div>

            <div class="success-stories-carousel">
                <div class="carousel-container" id="success-stories-container">
                    <!-- Success story cards will be populated by JavaScript -->
                </div>
                <div class="carousel-controls">
                    <button id="prev-story" class="carousel-control-btn">
                        <i class="ri-arrow-left-s-line"></i>
                    </button>
                    <div class="carousel-indicators" id="story-indicators">
                        <!-- Indicators will be populated by JavaScript -->
                    </div>
                    <button id="next-story" class="carousel-control-btn">
                        <i class="ri-arrow-right-s-line"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>


    <!-- Partners Section -->
    <section id="partners" class="section">
        <div class="container">
            <div class="text-center mb-16">
                <h2 class="section-title" data-i18n="partners.title">Our Partners</h2>
                <p class="section-subtitle" data-i18n="partners.subtitle">We collaborate with leading organizations across industries to build innovative ventures.</p>
            </div>
            <div class="partner-filter-container">
                <div class="partner-category-selector" id="partner-categories">
                    <!-- Categories will be populated by JavaScript -->
                </div>
            </div>

            <div class="partners-grid" id="partners-grid">
                <!-- Partner cards will be populated by JavaScript -->
            </div>

            <!-- Pagination Controls -->
            <div class="pagination-container flex justify-center items-center mt-8">
                <div class="pagination-controls flex items-center space-x-2">
                    <button id="prev-page" class="pagination-btn bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-1 rounded-md transition-colors">
                        <i class="ri-arrow-left-s-line"></i> <span data-i18n="partners.previous">Previous</span>
                    </button>
                    <div id="pagination-numbers" class="flex space-x-1">
                        <!-- Page numbers will be populated by JavaScript -->
                    </div>
                    <button id="next-page" class="pagination-btn bg-gray-200 hover:bg-gray-300 text-gray-700 px-3 py-1 rounded-md transition-colors">
                        <span data-i18n="partners.next">Next</span> <i class="ri-arrow-right-s-line"></i>
                    </button>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="./pages/contact.html"  class=" cursor-pointer bg-[#0056b3] text-white  p-3 rounded-md hover:bg-[--primary-color-3]  transition-all duration-300 ease-in-out hover:-translate-y-1.5 inline-block delay-100 " data-i18n="partners.becomePartner">Become a Partner</a>
            </div>
        </div>
    </section>

    <!-- About Us Section -->
    <section id="about" class="section bg-light">
        <div class="container">
            <div class="about-content">
                <div class="about-text">
                    <h2 class="h2" data-i18n="about.title">About Sahla Smart Solutions</h2>
                    <p class="about-description justify-content" data-i18n="about.description1">Sahla Smart Solutions is a forward-thinking venture studio and automation powerhouse based in the MENA region. Since 2021, we've been transforming ideas into scalable businesses and reshaping how organizations operate using automation,
                        AI, and data-driven strategies.</p>
                    <p class="about-description justify-content" data-i18n="about.description2">Our cross-functional team — engineers, entrepreneurs, and industry minds — collaborates to design, build, and launch future-ready solutions across multiple sectors. Whether we're developing the next-gen platform or automating legacy
                        workflows, we don't just follow trends—we set them.</p>

                    <div class="sahla-edge">
                        <h3 class="h3 mb-8" data-i18n="about.sahlaEdge">The Sahla Edge</h3>
                        <div class="edge-grid">
                            <div class="edge-item">
                                <div class="edge-icon">
                                    <i class="ri-rocket-2-line"></i>
                                </div>
                                <div class="edge-content">
                                    <h4 data-i18n="about.edge.fullStack.title">Full-Stack Venture Building</h4>
                                    <p data-i18n="about.edge.fullStack.description">From ideation to MVP and scale—we don't consult, we co-create.</p>
                                </div>
                            </div>
                            <div class="edge-item">
                                <div class="edge-icon">
                                    <i class="ri-robot-line"></i>
                                </div>
                                <div class="edge-content">
                                    <h4 data-i18n="about.edge.automationFirst.title">Automation-First Mindset</h4>
                                    <p data-i18n="about.edge.automationFirst.description">We embed automation and intelligence into every layer of a business.</p>
                                </div>
                            </div>
                            <div class="edge-item">
                                <div class="edge-icon">
                                    <i class="ri-cpu-line"></i>
                                </div>
                                <div class="edge-content">
                                    <h4 data-i18n="about.edge.techEnabled.title">Tech-Enabled, Market-Driven</h4>
                                    <p data-i18n="about.edge.techEnabled.description">We fuse AI, IoT, and software to solve real-world problems at scale.</p>
                                </div>
                            </div>
                            <div class="edge-item">
                                <div class="edge-icon">
                                    <i class="ri-database-2-line"></i>
                                </div>
                                <div class="edge-content">
                                    <h4 data-i18n="about.edge.dataDesign.title">Data, Design, and Discipline</h4>
                                    <p data-i18n="about.edge.dataDesign.description">Our ventures are born from research, shaped with design, and launched with precision.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="about-image">
                    <img src="https://images.unsplash.com/photo-1455849318743-b2233052fcff" alt="Sahla Team" class="rounded-img">
                </div>
            </div>
        </div>
    </section>

    <!-- f -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Company Info Column -->
                <div class="footer-info">
                    <a href="index.html" class="footer-logo">
                        <img src="assets/images/Negative Horizontal Version.svg" alt="Sahla Smart Solutions" class="logo-img" id="foot-logo">
                    </a>
                    <p class="footer-description" data-i18n="footer.description">Empowering businesses through innovative venture building and automation solutions in the MENA region.</p>
                    <div class="footer-social">
                        <a href="https://eg.linkedin.com/company/sahla-solutions" target="_blank" rel="noopener noreferrer"><i class="ri-linkedin-fill"></i></a>
                        <a href="https://x.com/sahlasolutions" target="_blank" rel="noopener noreferrer"><i class="ri-twitter-fill"></i></a>
                        <a href="https://www.facebook.com/SahlaSmartSolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-facebook-fill"></i></a>
                        <a href="https://www.instagram.com/sahlasolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-instagram-fill"></i></a>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div class="footer-links-column">
                    <h4 data-i18n="footer.quickLinks">Quick Links</h4>
                    <ul class="footer-links-list">
                        <li><a href="index.html" data-i18n="footer.home">Home</a></li>
                        <li><a href="./pages/about.html" data-i18n="footer.aboutUs">About Us</a></li>
                        <li><a href="./pages/ventures.html" data-i18n="footer.ourVentures">Our Ventures</a></li>
                        <li><a href="./pages/partners.html" data-i18n="nav.partners">Partners</a></li>
                        <li><a href="./pages/careers.html" data-i18n="nav.careers">Careers</a></li>
                        <li><a href="./pages/contact.html" data-i18n="footer.contactUs">Contact Us</a></li>
                    </ul>
                </div>

                <!-- Services Column -->
                <div class="footer-links-column">
                    <h4 data-i18n="footer.services">Services</h4>
                    <ul class="footer-links-list">
                        <li><a href="./pages/process-automation.html" data-i18n="nav.processAutomation">Process Automation</a></li>
                        <li><a href="./pages/technology-consulting.html" data-i18n="nav.technologyConsulting">Technology Consulting</a></li>
                        <!-- <li><a href="#">Innovation Strategy</a></li>
                        <li><a href="#">Market Research</a></li> -->
                    </ul>
                </div>

                <!-- Newsletter Column -->
                <div class="footer-newsletter">
                    <h4 data-i18n="footer.newsletter">Newsletter</h4>

                    <form class="mb-4">
                        <div class="flex">
                            <!-- <input type="email" placeholder="Your email" class="w-full px-4 py-2 bg-gray-800 border-none text-white text-sm rounded-l"> -->
                            <a href="javascript:void(0)" class="ml-onclick-form" onclick="ml('show', 'gturk0', true)" style="
                                display: inline-block;
                                padding: 8px 16px;
                                background-color: var(--primary-color-1);
                                color: white;
                                font-size: 16px;
                                font-family: sans-serif;
                                border: none;
                                border-radius: 6px;
                                text-decoration: none;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                                " onmouseover="this.style.backgroundColor='var(--primary-color-2)'" onmouseout="this.style.backgroundColor= 'var(--primary-color-3)'" data-i18n="footer.subscribeNewsletter">
                                Subscribe to Newsletter
                                </a>
                        </div>
                    </form>
                    <p data-i18n="footer.newsletterDescription">Subscribe to our newsletter to stay updated with our latest news and insights.</p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0" data-i18n="footer.copyright">© 2025 Sahla Smart Solutions. All rights reserved.</p>
                    <div class="flex space-x-6">
                        <a href="./pages/privacy-policy.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.privacyPolicy">Privacy Policy</a>
                        <a href="./pages/terms-of-service.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.termsOfService">Terms of Service</a>
                        <a href="./pages/cookies-policy.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.cookiesPolicy">Cookies Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Theme switch functionality
        const themeSwitch = document.getElementById('theme-switch');
        const html = document.documentElement;
        const navLogo = document.getElementById('nav-logo');
        const footLogo = document.getElementById('foot-logo');

        // Function to set and save theme
        const setTheme = (theme) => {
            html.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            themeSwitch.classList.toggle('dark', theme === 'dark');

            // Update logo based on theme
            if (theme === 'dark') {
                navLogo.src = './assets/images/Dark Horizontal Version.svg';
                footLogo.src = './assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = './assets/images/Main Horizontal Version.svg';
                footLogo.src = './assets/images/Main Horizontal Version.svg';
            }
        };

        // Set the correct theme class on icon container and logo after load
        document.addEventListener('DOMContentLoaded', () => {
            const currentTheme = html.getAttribute('data-theme');
            themeSwitch.classList.toggle('dark', currentTheme === 'dark');

            // Set initial logo based on theme
            if (currentTheme === 'dark') {
                navLogo.src = './assets/images/Dark Horizontal Version.svg';
                footLogo.src = './assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = './assets/images/Main Horizontal Version.svg';
                footLogo.src = './assets/images/Main Horizontal Version.svg';
            }
        });

        // Toggle theme on click
        themeSwitch.addEventListener('click', () => {
            const isCurrentlyDark = html.getAttribute('data-theme') === 'dark';
            const newTheme = isCurrentlyDark ? 'light' : 'dark';
            setTheme(newTheme);
        });

        // Watch for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                setTheme(e.matches ? 'dark' : 'light');
            }
        });

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navLinks = document.querySelector('.nav-links');

        mobileMenuToggle.addEventListener('change', function() {
            if (this.checked) {
                navLinks.style.display = 'flex';
            } else {
                navLinks.style.display = '';
            }
        });

        // Submenu toggle
        function toggleSubmenu(id) {
            const submenu = document.getElementById(id);
            submenu.classList.toggle('hidden');
        }

        // Custom checkbox toggle
        function toggleCheckbox(checkbox) {
            checkbox.classList.toggle('checked');
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                if (targetId === '#') return;

                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    const headerHeight = 80; // Approximate header height
                    const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });

                    // Close mobile menu if open
                    if (!mobileMenuToggle.checked) {
                        mobileMenuToggle.checked = false;
                        navLinks.style.display = '';
                    }
                }
            });
        });

        // Sticky header effect
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 50) {
                header.classList.add('shadow-md');
            } else {
                header.classList.remove('shadow-md');
            }
        });

        // Success Stories Carousel functionality
        function initializeSuccessStoriesCarousel() {
            console.log('Initializing success stories carousel...');
            const container = document.getElementById('success-stories-container');
            const indicatorsContainer = document.getElementById('story-indicators');
            const prevBtn = document.getElementById('prev-story');
            const nextBtn = document.getElementById('next-story');

            if (!container || !indicatorsContainer || !prevBtn || !nextBtn) {
                console.error('Success stories carousel elements not found');
                return;
            }

            let currentIndex = 0;
            const stories = successStoriesData;
            console.log('Stories data:', stories);

            // Clear existing content
            container.innerHTML = '';
            indicatorsContainer.innerHTML = '';

            // Create story cards
            console.log('Creating', stories.length, 'story cards');
            stories.forEach((story, index) => {
                console.log('Creating card for:', story.name);
                        // Create card
                        const card = document.createElement('div');
                        card.className = 'story-card';
                        if (index === 0) card.classList.add('visible');
                        card.innerHTML = `
                    <div class="story-card-content">
                        <div class="story-header">
                            <img src="${story.avatar}" alt="${story.name}" class="story-avatar">
                            <div class="story-info">
                                <h3>${story.name}</h3>
                                <p>${story.position}</p>
                            </div>
                        </div>
                        <p class="story-quote">"${story.quote}"</p>
                        <div class="story-metrics">
                            ${story.metrics.map(metric => `
                                <div class="metric-item">
                                    <div class="metric-value">${metric.value}</div>
                                    <div class="metric-label">${metric.label}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
                container.appendChild(card);

                // Create indicator
                const indicator = document.createElement('div');
                indicator.className = `indicator ${index === 0 ? 'active' : ''}`;
                indicator.addEventListener('click', () => goToSlide(index));
                indicatorsContainer.appendChild(indicator);
            });

            console.log('Created', container.children.length, 'cards and', indicatorsContainer.children.length, 'indicators');

            // Show first slide
            updateCarousel();

            // Event listeners
            prevBtn.addEventListener('click', () => goToSlide(currentIndex - 1));
            nextBtn.addEventListener('click', () => goToSlide(currentIndex + 1));

            // Auto-advance carousel
            let autoAdvanceInterval = setInterval(() => goToSlide(currentIndex + 1), 5000);

            // Pause auto-advance on hover
            container.addEventListener('mouseenter', () => clearInterval(autoAdvanceInterval));
            container.addEventListener('mouseleave', () => {
                autoAdvanceInterval = setInterval(() => goToSlide(currentIndex + 1), 5000);
            });

            function goToSlide(index) {
                // Handle wrapping around
                if (index < 0) index = stories.length - 1;
                if (index >= stories.length) index = 0;

                currentIndex = index;
                updateCarousel();
            }

            function updateCarousel() {
                console.log('Updating carousel to index:', currentIndex);

                // Check if we're in RTL mode
                const isRTL = document.documentElement.getAttribute('dir') === 'rtl';

                // Update container position - reverse direction for RTL
                const translateDirection = isRTL ? '' : '-';
                container.style.transform = `translateX(${translateDirection}${currentIndex * 100}%)`;

                // Update indicators
                document.querySelectorAll('.indicator').forEach((indicator, index) => {
                    indicator.classList.toggle('active', index === currentIndex);
                });

                // Update card visibility with animation
                document.querySelectorAll('.story-card').forEach((card, index) => {
                    card.classList.remove('visible');
                    if (index === currentIndex) {
                        setTimeout(() => {
                            card.classList.add('visible');
                        }, 100);
                    }
                });
            }
        }

        // Partners section functionality
        function initializePartnersSection() {
            const categoriesContainer = document.getElementById('partner-categories');
            const partnersGrid = document.getElementById('partners-grid');
            const paginationNumbers = document.getElementById('pagination-numbers');
            const prevPageBtn = document.getElementById('prev-page');
            const nextPageBtn = document.getElementById('next-page');

            let currentCategory = 'all';
            let currentPage = 1;
            const partnersPerPage = 6; // Number of partners to display per page
            let filteredPartners = [];
            let totalPages = 1;

            // Populate category buttons
            partnersData.categories.forEach(category => {
                const button = document.createElement('button');
                button.className = `filter-btn ${category.id === 'all' ? 'active' : ''}`;
                button.textContent = category.name;
                button.dataset.category = category.id;
                button.addEventListener('click', () => {
                    currentPage = 1; // Reset to first page when changing category
                    filterPartners(category.id);
                });
                categoriesContainer.appendChild(button);
            });

            // Function to filter partners
            function filterPartners(category) {
                currentCategory = category;

                // Update active button
                document.querySelectorAll('.filter-btn').forEach(btn => {
                    btn.classList.toggle('active', btn.dataset.category === category);
                });

                // Filter partners
                filteredPartners = category === 'all'
                    ? partnersData.partners
                    : partnersData.partners.filter(partner => partner.category === category);

                // Calculate total pages
                totalPages = Math.ceil(filteredPartners.length / partnersPerPage);

                // Update pagination
                updatePagination();

                // Display current page
                displayPartnersPage(currentPage);
            }

            // Function to display partners for the current page
            function displayPartnersPage(page) {
                // Clear grid
                partnersGrid.innerHTML = '';

                // Calculate start and end indices for the current page
                const startIndex = (page - 1) * partnersPerPage;
                const endIndex = Math.min(startIndex + partnersPerPage, filteredPartners.length);

                // Get partners for the current page
                const partnersForPage = filteredPartners.slice(startIndex, endIndex);

                // Add new cards with animation
                partnersForPage.forEach((partner, index) => {
                    const card = document.createElement('div');
                    card.className = 'partner-logo-card';
                    card.innerHTML = `
                        <div class="partner-logo-content">
                            <div class="partner-icon-wrapper">
                                <i class="${partner.icon}"></i>
                            </div>
                            <span class="partner-name">${partner.name}</span>
                        </div>
                    `;
                    partnersGrid.appendChild(card);

                    // Trigger animation after a small delay
                    setTimeout(() => {
                        card.classList.add('visible');
                    }, index * 50); // Stagger the animations
                });

                // Update pagination buttons state
                updatePaginationButtons();
            }

            // Function to update pagination numbers
            function updatePagination() {
                paginationNumbers.innerHTML = '';

                // Determine which page numbers to show
                let startPage = Math.max(1, currentPage - 2);
                let endPage = Math.min(totalPages, startPage + 4);

                // Adjust start page if we're near the end
                if (endPage - startPage < 4) {
                    startPage = Math.max(1, endPage - 4);
                }

                // Add first page if not included
                if (startPage > 1) {
                    addPageButton(1);
                    if (startPage > 2) {
                        addEllipsis();
                    }
                }

                // Add page numbers
                for (let i = startPage; i <= endPage; i++) {
                    addPageButton(i);
                }

                // Add last page if not included
                if (endPage < totalPages) {
                    if (endPage < totalPages - 1) {
                        addEllipsis();
                    }
                    addPageButton(totalPages);
                }
            }

            // Helper function to add a page button
            function addPageButton(pageNum) {
                const button = document.createElement('button');
                button.className = `pagination-number w-8 h-8 flex items-center justify-center rounded-md ${pageNum === currentPage ? 'bg-primary text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'}`;
                button.textContent = pageNum;
                button.addEventListener('click', () => {
                    if (pageNum !== currentPage) {
                        currentPage = pageNum;
                        displayPartnersPage(currentPage);
                        updatePagination();
                    }
                });
                paginationNumbers.appendChild(button);
            }

            // Helper function to add ellipsis
            function addEllipsis() {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'px-2 text-gray-500';
                ellipsis.textContent = '...';
                paginationNumbers.appendChild(ellipsis);
            }

            // Function to update pagination buttons state
            function updatePaginationButtons() {
                prevPageBtn.disabled = currentPage === 1;
                prevPageBtn.classList.toggle('opacity-50', currentPage === 1);
                prevPageBtn.classList.toggle('cursor-not-allowed', currentPage === 1);

                nextPageBtn.disabled = currentPage === totalPages;
                nextPageBtn.classList.toggle('opacity-50', currentPage === totalPages);
                nextPageBtn.classList.toggle('cursor-not-allowed', currentPage === totalPages);
            }

            // Add event listeners for pagination buttons
            prevPageBtn.addEventListener('click', () => {
                if (currentPage > 1) {
                    currentPage--;
                    displayPartnersPage(currentPage);
                    updatePagination();
                }
            });

            nextPageBtn.addEventListener('click', () => {
                if (currentPage < totalPages) {
                    currentPage++;
                    displayPartnersPage(currentPage);
                    updatePagination();
                }
            });

            // Initialize with all partners
            filterPartners('all');
        }

        // Initialize all sections when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM loaded, initializing sections...');

            // Initialize success stories carousel
            setTimeout(() => {
                if (typeof successStoriesData !== 'undefined' && successStoriesData.length > 0) {
                    console.log('Success stories data available, initializing carousel...');
                    initializeSuccessStoriesCarousel();
                } else {
                    console.error('Success stories data not available');
                    console.log('successStoriesData:', typeof successStoriesData, successStoriesData);
                }
            }, 100);

            // Initialize partners section
            initializePartnersSection();

            // Initialize pagination arrows
            setTimeout(() => {
                updatePaginationArrows();
            }, 200);
        });

        // Function to update pagination arrows for RTL
        function updatePaginationArrows() {
            const isRTL = document.documentElement.getAttribute('dir') === 'rtl';
            const prevPageBtn = document.getElementById('prev-page');
            const nextPageBtn = document.getElementById('next-page');

            if (prevPageBtn && nextPageBtn) {
                if (isRTL) {
                    // In RTL, swap the arrows
                    prevPageBtn.innerHTML = '<i class="ri-arrow-right-s-line"></i> <span data-i18n="partners.previous">Previous</span>';
                    nextPageBtn.innerHTML = '<span data-i18n="partners.next">Next</span> <i class="ri-arrow-left-s-line"></i>';
                } else {
                    // In LTR, normal arrows
                    prevPageBtn.innerHTML = '<i class="ri-arrow-left-s-line"></i> <span data-i18n="partners.previous">Previous</span>';
                    nextPageBtn.innerHTML = '<span data-i18n="partners.next">Next</span> <i class="ri-arrow-right-s-line"></i>';
                }
            }
        }



        // Listen for language changes and update carousel
        window.addEventListener('languageChanged', (event) => {
            console.log('Language changed, updating carousel for RTL/LTR:', event.detail);

            // Update pagination arrows
            updatePaginationArrows();

            // Reinitialize carousel to handle RTL/LTR changes
            setTimeout(() => {
                if (typeof successStoriesData !== 'undefined' && successStoriesData.length > 0) {
                    initializeSuccessStoriesCarousel();
                }
            }, 100);
        });
    </script>

    <!-- i18n Implementation -->
    <script src="./assets/js/i18n.js"></script>
</body>

</html>