<?php
// Autoload dependencies using Composer
require_once __DIR__ . '/../vendor/autoload.php'; // Adjust the path to vendor directory

// Load the .env file located in the root folder (above 'contact-api')
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../');  // This points to the root folder
$dotenv->load();

// Now, you can safely access your environment variables:
return [
    'smtp_host'     => $_ENV['SMTP_HOST'],
    'smtp_username' => $_ENV['SMTP_USERNAME'],
    'smtp_password' => $_ENV['SMTP_PASSWORD'],
    'smtp_port'     => $_ENV['SMTP_PORT'],
    'from_email'    => $_ENV['FROM_EMAIL'],
    'from_name'     => $_ENV['FROM_NAME'],
];
