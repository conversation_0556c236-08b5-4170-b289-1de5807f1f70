// Ventures data structure
const venturesData = {
    ventures: [{
        id: "mahally",
        title: "Mahally – Local Brands Marketplace",
        status: "In Development",
        cover_image: "mahally-cover.jpg",
        overview: "A marketplace platform that connects local Egyptian brands to a broader customer base — focusing on discoverability, trust, and scale.",
        features: [
            "Shop & Merchant Accounts",
            "Brand Stories",
            "Localized Search"
        ],
        vision: "Empowering local economies through digital infrastructure.",
        content: `
# Mahally – Local Brands Marketplace

## Overview
A marketplace platform that connects local Egyptian brands to a broader customer base — focusing on discoverability, trust, and scale.

## Core Features
- **Shop & Merchant Accounts**: Easy onboarding for local merchants with customizable storefronts
- **Brand Stories**: Interactive space for brands to share their journey and values
- **Localized Search**: AI-powered search that understands local context and preferences

## Vision
Empowering local economies through digital infrastructure. We aim to create a sustainable ecosystem where local brands can thrive in the digital economy.

## Impact
By connecting local producers directly with consumers, Mahally reduces intermediaries, increases profit margins for creators, and preserves cultural heritage through traditional crafts and products.
            `
    },
    {
        id: "xr-command-centre",
        title: "XR Command Centre – ERP Addon for Smart Oversight",
        status: "In Development",
        cover_image: "xr-command-cover.jpg",
        overview: "An executive control layer that integrates with existing ERP systems to offer real-time, AI-assisted monitoring and strategic intervention.",
        features: [
            "Visual Dashboards",
            "AI Summarization & Forecasting",
            "Departmental Command Modules"
        ],
        target_audience: "Executives, Directors, Strategy Teams",
        content: `
# XR Command Centre – ERP Addon for Smart Oversight

## Overview
An executive control layer that integrates with existing ERP systems to offer real-time, AI-assisted monitoring and strategic intervention.

## Core Features
- **Visual Dashboards**: Immersive 3D visualization of business data and operations
- **AI Summarization & Forecasting**: AI-powered insights and predictive analytics
- **Departmental Command Modules**: Specialized interfaces for different business functions

## Target Audience
Executives, Directors, Strategy Teams who need comprehensive oversight with actionable insights.

## Technology Stack
- Extended Reality (XR) for immersive data visualization
- Integration APIs for major ERP systems
- Machine learning models for predictive analytics
            `
    },
    {
        id: "automation-as-a-service",
        title: "Automation-as-a-Service",
        status: "In Planning",
        cover_image: "automation-cover.jpg",
        overview: "A platform offering scheduled automations, AI agents, and cognitive automation for businesses of all sizes.",
        modules: [
            "Workflow Automation",
            "Rule-based & Event-based Triggers",
            "Multi-agent Architectures"
        ],
        phase: "Internal training program and architecture planning underway",
        content: `
# Automation-as-a-Service

## Overview
A platform offering scheduled automations, AI agents, and cognitive automation for businesses of all sizes.

## Core Modules
- **Workflow Automation**: Visual workflow builder for no-code automation
- **Rule-based & Event-based Triggers**: Flexible triggering mechanisms for automated processes
- **Multi-agent Architectures**: Orchestration of specialized AI agents working together

## Current Phase
Internal training program and architecture planning underway. Our team is developing the core infrastructure while refining our understanding of diverse automation needs across industries.

## Market Opportunity
As businesses continue to digitize, the demand for accessible automation solutions grows. Our platform aims to democratize automation technologies for organizations without dedicated engineering teams.
            `
    },
    {
        id: "smart-pilgrimage",
        title: "Smart Pilgrimage Platform",
        status: "Operational (Soft Launch Phase)",
        cover_image: "pilgrimage-cover.jpg",
        overview: "Tech solutions designed for the Hajj and Umrah sectors — combining on-ground hotel services with digital guides, booking platforms, and behavior tracking.",
        components: [
            "Accommodation Management",
            "AR/AI Pilgrim Assistant",
            "Data Collection & Analytics Layer"
        ],
        vision: "Become the go-to operational and tech layer of pilgrimage travel by 2026",
        content: `
# Smart Pilgrimage Platform

## Overview
Tech solutions designed for the Hajj and Umrah sectors — combining on-ground hotel services with digital guides, booking platforms, and behavior tracking.

## Components
- **Accommodation Management**: Smart booking and management system for pilgrimage accommodation
- **AR/AI Pilgrim Assistant**: Augmented reality guidance and AI assistant for pilgrims
- **Data Collection & Analytics Layer**: Insights platform for service providers and authorities

## Vision
Become the go-to operational and tech layer of pilgrimage travel by 2026, enhancing the spiritual journey through respectful and unobtrusive technology.

## Current Status
Operational in soft launch phase with select partners. Initial user feedback has been overwhelmingly positive, with particular appreciation for the AR guidance features.
            `
    }
    ],
    upcoming: [{
        id: "ai-workspace",
        title: "AI Workspace",
        planned_date: "2026",
        description: "An integrated environment for fully AI-run operations across internal departments."
    },
    {
        id: "sahla-labs",
        title: "Sahla Labs",
        stage: "Concept Stage",
        description: "A studio for testing micro-ideas and deploying modular tools under a unified infrastructure."
    }
    ]
};