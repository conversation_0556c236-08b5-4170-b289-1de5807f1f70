<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Message Sent - Sahla Smart Solutions</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#0056b3',secondary:'#4dabf7'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/navigation.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">
    <style>
        .success-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem;
        }

        .success-icon {
            width: 80px;
            height: 80px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            font-size: 2rem;
        }

        .countdown {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-top: 1rem;
        }
    </style>
</head>
<body class="bg-white">
    <div class="success-container">
        <div>
            <div class="success-icon">
                <i class="ri-check-line"></i>
            </div>
            <h1 class="h1 mb-4" style="color: var(--primary-color-1)">Thank You!</h1>
            <p class="text-xl mb-8">Your message has been sent successfully. We'll get back to you soon.</p>
            <p class="countdown">Redirecting in <span id="timer">5</span> seconds...</p>
        </div>
    </div>

    <script>
        // Countdown timer
        let timeLeft = 5;
        const timerElement = document.getElementById('timer');
        
        const countdown = setInterval(() => {
            timeLeft--;
            timerElement.textContent = timeLeft;
            
            if (timeLeft <= 0) {
                clearInterval(countdown);
                window.location.href = '/';
            }
        }, 1000);
    </script>
</body>
</html>