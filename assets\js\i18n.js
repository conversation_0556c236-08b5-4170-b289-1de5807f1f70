/**
 * Internationalization (i18n) Implementation for Sahla Website
 * Supports English (LTR) and Arabic (RTL) languages
 */

class SahlaI18n {
    constructor() {
        this.currentLanguage = 'en';
        this.isInitialized = false;
        this.fonts = {
            en: 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap',
            ar: 'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap'
        };
        this.init();
    }

    async init() {
        try {
            // Get saved language or default to English
            const savedLanguage = localStorage.getItem('sahla-language') || 'en';
            console.log('Initializing i18n with language:', savedLanguage);

            // Determine the correct path for translation files
            const currentPath = window.location.pathname;
            const isInSubfolder = currentPath.includes('/pages/');
            const loadPath = isInSubfolder ? '../locales/{{lng}}/translation.json' : './locales/{{lng}}/translation.json';

            console.log('Loading translations from:', loadPath);

            // Check if i18nextHttpBackend is available
            if (typeof i18nextHttpBackend === 'undefined') {
                throw new Error('i18nextHttpBackend is not loaded');
            }

            // Initialize i18next
            await i18next
                .use(i18nextHttpBackend)
                .init({
                    lng: savedLanguage,
                    fallbackLng: 'en',
                    debug: true, // Enable debug for troubleshooting
                    backend: {
                        loadPath: loadPath,
                        crossDomain: true,
                        requestOptions: {
                            cache: 'no-cache'
                        }
                    },
                    interpolation: {
                        escapeValue: false
                    }
                });

            this.currentLanguage = savedLanguage;
            this.isInitialized = true;

            console.log('i18next initialized, available languages:', i18next.languages);
            console.log('Current language:', i18next.language);

            // Apply initial language settings
            this.applyLanguage(savedLanguage);

            // Set up event listeners
            this.setupEventListeners();

            // Translate the page
            this.translatePage();

            console.log('Sahla i18n initialized successfully');
        } catch (error) {
            console.error('Failed to initialize i18n:', error);
        }
    }

    setupEventListeners() {
        // Language toggle buttons (both desktop and mobile)
        const langButtons = document.querySelectorAll('.lang-btn');
        console.log('Found language buttons:', langButtons.length);

        langButtons.forEach(btn => {
            console.log('Setting up listener for button:', btn.getAttribute('data-lang'));
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const lang = e.target.getAttribute('data-lang');
                console.log('Language button clicked:', lang);
                if (lang && lang !== this.currentLanguage) {
                    this.changeLanguage(lang);

                    // Close mobile menu if open
                    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
                    if (mobileMenuToggle && mobileMenuToggle.checked) {
                        mobileMenuToggle.checked = false;
                    }
                } else {
                    console.log('Same language or invalid language:', lang);
                }
            });
        });
    }

    async changeLanguage(language) {
        console.log('Attempting to change language to:', language);

        if (!this.isInitialized) {
            console.warn('i18n not initialized yet');
            return;
        }

        try {
            console.log('Changing i18next language to:', language);
            // Change language in i18next
            await i18next.changeLanguage(language);

            // Update current language
            this.currentLanguage = language;

            // Save to localStorage
            localStorage.setItem('sahla-language', language);

            console.log('Language changed in i18next, now applying to page');

            // Apply language settings
            this.applyLanguage(language);

            // Translate the page
            this.translatePage();

            console.log(`Language successfully changed to: ${language}`);

            // Dispatch custom event for language change
            window.dispatchEvent(new CustomEvent('languageChanged', {
                detail: { language, isRTL: language === 'ar' }
            }));
        } catch (error) {
            console.error('Failed to change language:', error);
        }
    }

    applyLanguage(language) {
        const html = document.documentElement;
        const body = document.body;

        // Update HTML attributes
        html.setAttribute('lang', language);
        html.setAttribute('dir', language === 'ar' ? 'rtl' : 'ltr');

        // Update body class for styling
        body.classList.remove('lang-en', 'lang-ar');
        body.classList.add(`lang-${language}`);

        // Load appropriate font
        this.loadFont(language);

        // Update active language button
        this.updateLanguageButtons(language);

        // Apply RTL/LTR specific styles
        this.applyDirectionStyles(language);
    }

    loadFont(language) {
        const fontLink = document.getElementById('google-fonts');
        if (fontLink) {
            fontLink.href = this.fonts[language];
        }

        // Apply font family to body
        const fontFamily = language === 'ar' ? 'Cairo, sans-serif' : 'Inter, sans-serif';
        document.body.style.fontFamily = fontFamily;
    }

    updateLanguageButtons(language) {
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(btn => {
            const btnLang = btn.getAttribute('data-lang');
            if (btnLang === language) {
                btn.classList.add('active');
                btn.classList.remove('inactive');
            } else {
                btn.classList.remove('active');
                btn.classList.add('inactive');
            }
        });
    }

    applyDirectionStyles(language) {
        const isRTL = language === 'ar';

        // Update HTML direction attribute
        document.documentElement.setAttribute('dir', isRTL ? 'rtl' : 'ltr');

        // Update HTML lang attribute
        document.documentElement.setAttribute('lang', language);

        // Update Tailwind direction classes if needed
        if (isRTL) {
            // Add RTL-specific adjustments
            document.body.classList.add('rtl');
        } else {
            document.body.classList.remove('rtl');
        }

        // Ensure language buttons adapt to current theme
        this.updateLanguageButtonsForTheme();
    }

    updateLanguageButtonsForTheme() {
        // This method ensures language buttons properly reflect the current theme
        // The CSS variables will handle the actual styling, but we can trigger
        // a re-render if needed
        const langButtons = document.querySelectorAll('.lang-btn');
        langButtons.forEach(btn => {
            // Force a style recalculation by toggling a class
            btn.classList.add('theme-updating');
            setTimeout(() => {
                btn.classList.remove('theme-updating');
            }, 10);
        });
    }

    translatePage() {
        if (!this.isInitialized) {
            console.log('Cannot translate page - i18n not initialized');
            return;
        }

        console.log('Starting page translation...');

        // Translate all elements with data-i18n attribute
        const elements = document.querySelectorAll('[data-i18n]');
        console.log(`Found ${elements.length} elements to translate`);

        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = i18next.t(key);

            console.log(`Translating key "${key}" to: "${translation}"`);

            if (translation && translation !== key) {
                // Handle HTML content (for keys that contain <br> tags)
                if (translation.includes('<br>')) {
                    element.innerHTML = translation;
                } else {
                    element.textContent = translation;
                }
            } else {
                console.warn(`No translation found for key: ${key}`);
            }
        });

        // Translate placeholders
        const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
        console.log(`Found ${placeholderElements.length} placeholder elements to translate`);

        placeholderElements.forEach(element => {
            const key = element.getAttribute('data-i18n-placeholder');
            const translation = i18next.t(key);

            if (translation && translation !== key) {
                element.setAttribute('placeholder', translation);
            }
        });

        // Translate titles and alt texts
        const titleElements = document.querySelectorAll('[data-i18n-title]');
        titleElements.forEach(element => {
            const key = element.getAttribute('data-i18n-title');
            const translation = i18next.t(key);

            if (translation && translation !== key) {
                element.setAttribute('title', translation);
            }
        });

        console.log('Page translation completed');
    }

    // Utility method to get translation
    t(key, options = {}) {
        if (!this.isInitialized) {
            console.warn('i18n not initialized yet');
            return key;
        }
        return i18next.t(key, options);
    }

    // Get current language
    getCurrentLanguage() {
        return this.currentLanguage;
    }

    // Check if current language is RTL
    isRTL() {
        return this.currentLanguage === 'ar';
    }
}

// Initialize i18n when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.sahlaI18n = new SahlaI18n();

    // Listen for theme changes to update language buttons
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
                if (window.sahlaI18n) {
                    window.sahlaI18n.updateLanguageButtonsForTheme();
                }
            }
        });
    });

    observer.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-theme']
    });
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SahlaI18n;
}
