/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background-color: var(--background-color);
    /* border-bottom: 1px solid var(--border-color); */
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.nav-logo {
    display: flex;
    align-items: center;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color-1);
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0%;
    height: 2px;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--primary-color-1);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* Dropdown menu */
.dropdown {
    position: relative;
}

.dropdown-content {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.5rem 0;
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
}

.dropdown:hover .dropdown-content {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: block;
    padding: 0.5rem 1rem;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.dropdown-link:hover {
    background-color: var(--background-color-light);
    color: var(--primary-color-1);
}

/* Mobile menu */
.mobile-menu-toggle {
    display: none;
}

.mobile-menu-button {
    display: none;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 21px;
    cursor: pointer;
}

.mobile-menu-button span {
    display: block;
    height: 3px;
    background-color: var(--text-color);
    border-radius: 3px;
    transition: all 0.3s ease;
}

/* Button in navigation */
.nav-links .btn {
    margin-left: 1rem;
}

/* Service link */
.service-link {
    display: inline-flex;
    align-items: center;
    color: var(--primary-color-1);
    text-decoration: none;
    font-weight: 500;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.service-link i {
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.service-link:hover {
    color: var(--primary-color-2);
}

.service-link:hover i {
    transform: translateX(3px);
}

/* Language Toggle Styles */
.language-toggle-desktop {
    display: flex;
    align-items: center;
}

.language-toggle-mobile {
    display: none;
    justify-content: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.language-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    padding: 0.25rem 0.5rem;
    background: var(--background-color);
    transition: all 0.3s ease;
}

.lang-btn {
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    background: transparent;
    cursor: pointer;
    color: var(--text-color);
}

.lang-btn:hover {
    background-color: var(--primary-color-1);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.lang-btn.active {
    background-color: var(--primary-color-1);
    color: white;
    box-shadow: var(--shadow-sm);
}

.lang-btn.inactive {
    color: var(--text-color-light);
    background: transparent;
}

.lang-btn.inactive:hover {
    background-color: var(--background-color-light);
    color: var(--text-color);
}

.lang-separator {
    color: var(--text-color-light);
    font-weight: 300;
    transition: color 0.3s ease;
}

/* Dark mode specific adjustments for language toggle */
[data-theme="dark"] .language-toggle {
    border-color: var(--border-color);
    background: var(--background-color);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .lang-btn {
    color: var(--text-color);
}

[data-theme="dark"] .lang-btn:hover {
    background-color: var(--primary-color-1);
    color: white;
    box-shadow: var(--shadow-sm);
}

[data-theme="dark"] .lang-btn.active {
    background-color: var(--primary-color-1);
    color: white;
    box-shadow: var(--shadow-sm);
}

[data-theme="dark"] .lang-btn.inactive {
    color: var(--text-color-light);
    background: transparent;
}

[data-theme="dark"] .lang-btn.inactive:hover {
    background-color: var(--background-color-light);
    color: var(--text-color);
}

[data-theme="dark"] .lang-separator {
    color: var(--text-color-light);
}

/* Navigation CTA Button */
.nav-cta-btn {
    background: var(--primary-color-1);
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 0.5rem;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-block;
}

.nav-cta-btn:hover {
    background-color: var(--primary-color-2);
    transform: translateY(-1px);
}

.nav-divider {
    display: flex;
    align-items: center;
    padding: 0 1rem;
}

/* RTL Navigation Support */
[dir="rtl"] .nav {
    flex-direction: row-reverse;
}

[dir="rtl"] .nav-links {
    flex-direction: row-reverse;
}

[dir="rtl"] .nav-logo {
    margin-left: 0;
    margin-right: 0;
}

[dir="rtl"] .dropdown-content {
    left: auto;
    right: 0;
    text-align: right;
}

[dir="rtl"] .dropdown-link {
    text-align: right;
    padding-right: 1rem;
    padding-left: 1rem;
}

[dir="rtl"] .language-toggle {
    flex-direction: row-reverse;
}

[dir="rtl"] .language-toggle-desktop {
    margin-left: 0;
    margin-right: 1rem;
}

[dir="rtl"] .nav-link::after {
    left: 50%;
    transform: translateX(-50%);
}

[dir="rtl"] .service-link i {
    margin-left: 0;
    margin-right: 0.5rem;
}

[dir="rtl"] .service-link:hover i {
    transform: translateX(-3px);
}

/* Responsive styles */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: none;
    }

    .mobile-menu-button {
        display: flex;
    }

    .language-toggle-desktop {
        display: none;
    }

    .language-toggle-mobile {
        display: flex;
    }

    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        flex-direction: column;
        background-color: var(--background-color);
        padding: 1rem;
        border-top: 1px solid var(--border-color);
        box-shadow: var(--shadow-md);
        gap: 1rem;
    }

    .mobile-menu-toggle:checked ~ .nav-links {
        display: flex;
    }

    .dropdown-content {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        border: none;
        box-shadow: none;
        padding: 0;
        margin-top: 0.5rem;
        background-color: var(--background-color-light);
    }

    .dropdown-link {
        padding-left: 2rem;
    }

    .nav-link::after {
        display: none;
    }

    .nav-cta-btn {
        margin: 0;
        width: 100%;
        text-align: center;
        display: block;
    }

    .nav-divider {
        display: none;
    }

    /* Mobile Dark Mode Language Toggle */
    [data-theme="dark"] .language-toggle-mobile .language-toggle {
        border-color: var(--border-color);
        background: var(--background-color);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
    }

    [data-theme="dark"] .language-toggle-mobile .lang-btn {
        color: var(--text-color);
    }

    [data-theme="dark"] .language-toggle-mobile .lang-btn:hover {
        background-color: var(--primary-color-1);
        color: white;
    }

    [data-theme="dark"] .language-toggle-mobile .lang-btn.active {
        background-color: var(--primary-color-1);
        color: white;
    }

    [data-theme="dark"] .language-toggle-mobile .lang-separator {
        color: var(--text-color-light);
    }

    /* RTL Mobile Navigation */
    [dir="rtl"] .nav-links {
        text-align: right;
        flex-direction: column;
    }

    [dir="rtl"] .nav-link {
        text-align: right;
    }

    [dir="rtl"] .dropdown-link {
        padding-right: 2rem;
        padding-left: 1rem;
        text-align: right;
    }

    [dir="rtl"] .nav-cta-btn {
        text-align: center;
    }

    [dir="rtl"] .mobile-menu-button {
        order: -1;
    }
}