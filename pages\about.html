<!DOCTYPE html>
<html lang="en">

<head>     <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico?v=2">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>a Smart Solutions - About us</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0056b3',
                        secondary: '#4dabf7'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/navigation.css">

    <!-- Dynamic Font Loading -->
    <link id="google-fonts" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- i18next Core and HTTP Backend -->
    <script src="https://unpkg.com/i18next@23.7.16/dist/umd/i18next.min.js"></script>
    <script src="https://unpkg.com/i18next-http-backend@2.4.2/i18nextHttpBackend.min.js"></script>

    <script src="../assets/js/partners-data.js"></script>
    <script src="../assets/js/success-stories-data.js"></script>
    <script src="../assets/js/collaboration-areas.js"></script>
    <style>
         :where([class^="ri-"])::before {
            content: "\f3c2";
        }

        .partners-hero {
            background-image: linear-gradient(to bottom, #111827d8, #22267a8f), url('https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg');
            background-size: 100% auto;
            background-position: center top 30%;
        }

        .process-line {
            background: linear-gradient(90deg, var(--primary-color-1) 0%, var(--secondary-color-1) 100%);
            height: 2px;
            position: absolute;
            top: 3rem;
            left: 2.5rem;
            right: 2.5rem;
            z-index: 0;
        }

        .process-step {
            z-index: 1;
            position: relative;
        }

        .process-number {
            width: 3rem;
            height: 3rem;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: white;
            border: 2px solid var(--primary-color-1);
            color: var(--primary-color-1);
            font-weight: 700;
        }
        /* Partner section styles */

        .partners-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .partner-logo-card {
            aspect-ratio: 1;
            width: 100%;
            max-width: 200px;
            margin: 0 auto;
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .partner-logo-card.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .partner-logo-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
            background: var(--bg-light);
            border-radius: 1rem;
            transition: all 0.3s ease;
        }

        .partner-logo-content:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .partner-icon-wrapper {
            width: 64px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 2rem;
            color: var(--primary-color);
        }

        .pagination-btn {
            background-color: var(--background-color-light);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            cursor: pointer;
        }

        .pagination-btn:hover {
            background-color: var(--background-color);
            color: var(--text-color);
        }

        .pagination-btn:active {
            background-color: var(--background-color);
            color: var(--text-color);
        }

        .pagination-number {
            background-color: var(--background-color-light);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            cursor: pointer;
        }

        .pagination-number:hover {
            background-color: var(--background-color);
            color: var(--text-color);
        }

        .pagination-number:active {
            background-color: var(--background-color);
            color: var(--text-color);
        }

        .partner-name {
            text-align: center;
            font-weight: 500;
            color: var(--text-color);
        }

        .filter-btn {
            padding: 0.5rem 1.5rem;
            border-radius: 2rem;
            background: transparent;
            color: var(--primary-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn:hover {
            background: var(--primary-color);
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .filter-btn.active {
            background: var(--primary-color-1);
            color: white;
            border-color: var(--primary-color);
        }
        /* Sahla Edge Styles */

        .sahla-edge {
            margin-top: 3rem;
        }

        .edge-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .edge-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding: 1.5rem;
            background: var(--bg-light);
            border-radius: 1rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .edge-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .edge-icon {
            width: 48px;
            height: 48px;
            min-width: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            color: var(--primary-color-1);
            border-radius: 12px;
            font-size: 1.5rem;
        }

        .edge-content h4 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-color);
        }

        .edge-content p {
            font-size: 0.9rem;
            line-height: 1.5;
            color: var(--text-light);
            margin: 0;
        }

        @media (max-width: 768px) {
            .edge-grid {
                grid-template-columns: 1fr;
            }
        }

        .about-hero {
            background-image: linear-gradient(to bottom, #111827d8, #22267a8f), url('https://images.pexels.com/photos/450062/pexels-photo-450062.jpeg');
            background-size: cover;
            background-position: center bottom 40%;
        }

        .timeline {
            position: relative;
            padding-left: 2rem;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0;
            bottom: 0;
            width: 2.5px;
            background: #d1d5db; /* Tailwind gray-300, visible on both themes */
            z-index: 0;
        }

        .timeline-item {
            position: relative;
            padding-bottom: 2rem;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -2.0rem;
            top: 0.25rem;
            width: 1.2rem;
            height: 1.2rem;
            border-radius: 50%;
            background: #111827; /* Always dark in light mode */
            border: 3px solid var(--primary-color);
            box-shadow: 0 0 0 4px #d1d5db;
            z-index: 1;
        }
        [data-theme="dark"] .timeline-item::before {
            background: #fff;
        }
        .team-member-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .team-member-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .process-step {
            position: relative;
            padding: 2rem;
            background: var(--background-color-light);
            border-radius: 1rem;
            transition: transform 0.3s ease;
        }

        .process-step:hover {
            transform: translateY(-5px);
        }

        .process-step::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border-radius: 1rem 1rem 0 0;
        }

        /* Language Toggle Styles */
        .language-toggle-desktop {
            display: flex;
            align-items: center;
            margin-left: 1rem;
        }

        .language-toggle-mobile {
            display: none;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .language-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.25rem 0.5rem;
            background: var(--background-color);
            transition: all 0.3s ease;
        }

        .lang-btn {
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            background: transparent;
            cursor: pointer;
            color: var(--text-color);
        }

        .lang-btn:hover {
            background-color: var(--primary-color-1);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        .lang-btn.active {
            background-color: var(--primary-color-1);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .lang-btn.inactive {
            color: var(--text-color-light);
            background: transparent;
        }

        .lang-btn.inactive:hover {
            background-color: var(--background-color-light);
            color: var(--text-color);
        }

        .lang-separator {
            color: var(--text-color-light);
            font-weight: 300;
            transition: color 0.3s ease;
        }

        /* Navigation CTA Button */
        .nav-cta-btn {
            background: var(--primary-color-1);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-cta-btn:hover {
            background-color: var(--primary-color-2);
            transform: translateY(-1px);
        }

        .nav-divider {
            display: flex;
            align-items: center;
            padding: 0 1rem;
        }

        /* Dark mode specific adjustments */
        [data-theme="dark"] .language-toggle {
            border-color: var(--border-color);
            background: var(--background-color);
        }

        [data-theme="dark"] .lang-btn {
            color: var(--text-color);
        }

        [data-theme="dark"] .lang-btn:hover {
            background-color: var(--primary-color-1);
            color: white;
        }

        [data-theme="dark"] .lang-btn.active {
            background-color: var(--primary-color-1);
            color: white;
        }

        [data-theme="dark"] .lang-btn.inactive {
            color: var(--text-color-light);
            background: transparent;
        }

        [data-theme="dark"] .lang-btn.inactive:hover {
            background-color: var(--background-color-light);
            color: var(--text-color);
        }

        [data-theme="dark"] .lang-separator {
            color: var(--text-color-light);
        }

        /* RTL Support */
        [dir="rtl"] {
            text-align: right;
        }

        /* Navigation RTL fixes */
        [dir="rtl"] .nav {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .nav-links {
            flex-direction: row-reverse;
            gap: 2rem;
        }

        [dir="rtl"] .nav-logo {
            margin-left: 0;
            margin-right: 0;
        }

        [dir="rtl"] .nav-links .dropdown {
            text-align: right;
        }

        [dir="rtl"] .dropdown-content {
            left: auto;
            right: 0;
            text-align: right;
        }

        [dir="rtl"] .dropdown-link {
            text-align: right;
        }

        [dir="rtl"] .nav-divider {
            padding: 0 1rem;
        }

        [dir="rtl"] .language-toggle-desktop {
            margin-left: 0;
            margin-right: 1rem;
        }

        [dir="rtl"] .language-toggle {
            flex-direction: row-reverse;
        }

        /* Mobile RTL Navigation */
        [dir="rtl"] .mobile-menu-button {
            order: -1;
        }

        [dir="rtl"] .nav-links {
            text-align: right;
        }

        [dir="rtl"] .nav-link {
            text-align: right;
        }

        [dir="rtl"] .nav-cta-btn {
            text-align: center;
        }

        [dir="rtl"] .flex {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .space-x-4 > * + * {
            margin-left: 0;
            margin-right: 1rem;
        }

        /* Mobile Navigation Responsive Styles */
        @media (max-width: 768px) {
            .language-toggle-desktop {
                display: none;
            }

            .language-toggle-mobile {
                display: flex;
                justify-content: center;
            }

            .nav-divider {
                display: none;
            }

            .nav-cta-btn {
                width: 100%;
                text-align: center;
                margin-top: 0.5rem;
            }

            /* RTL Mobile Fixes */
            [dir="rtl"] .nav-links {
                text-align: right;
            }

            [dir="rtl"] .nav-link {
                text-align: right;
            }

            [dir="rtl"] .dropdown-link {
                text-align: right;
            }

            [dir="rtl"] .nav-cta-btn {
                text-align: center;
            }
        }

        /* Arabic Font Support */
        .lang-ar {
            font-family: 'Cairo', sans-serif;
        }

        .lang-en {
            font-family: 'Inter', sans-serif;
        }
    </style>
    <!-- MailerLite Universal -->
    <script>
        (function(w, d, e, u, f, l, n) {
            w[f] = w[f] || function() {
                    (w[f].q = w[f].q || [])
                    .push(arguments);
                }, l = d.createElement(e), l.async = 1, l.src = u,
                n = d.getElementsByTagName(e)[0], n.parentNode.insertBefore(l, n);
        })
        (window, document, 'script', 'https://assets.mailerlite.com/js/universal.js', 'ml');
        ml('account', '1446707');
    </script>
    <!-- End MailerLite Universal -->
</head>

<body class="bg-white overflow-x-hidden">
    <!-- Header & Navigation -->
    <header class="header">
        <div class="container">
            <nav class="nav">
                <a href="../index.html" class="nav-logo">
                    <img src="../assets/images/Main Horizontal Version.svg" alt="Sahla Logo" class="logo-img" id="nav-logo">
                </a>
                <input type="checkbox" id="mobile-menu-toggle" class="mobile-menu-toggle">
                <label for="mobile-menu-toggle" class="mobile-menu-button">
                    <span></span>
                    <span></span>
                    <span></span>
                </label>
                <div class="nav-links">
                    <div class="dropdown">
                        <a href="#services" class="nav-link" data-i18n="nav.services">Services</a>
                        <div class="dropdown-content">
                            <a href="./process-automation.html" class="dropdown-link" data-i18n="nav.processAutomation">Process Automation</a>
                            <a href="./technology-consulting.html" class="dropdown-link" data-i18n="nav.technologyConsulting">Technology Consulting</a>
                        </div>
                    </div>
                    <a href="./ventures.html" class="nav-link" data-i18n="nav.ventures">Our Ventures</a>
                    <a href="./partners.html" class="nav-link" data-i18n="nav.partners">Partners</a>
                    <a href="./careers.html" class="nav-link" data-i18n="nav.careers">Careers</a>
                    <a href="./blog.html" class="nav-link" data-i18n="nav.blog">Blog</a>
                    <a href="./about.html" class="nav-link" data-i18n="nav.about">About us</a>
                    <div class="nav-divider">
                        <div class="h-6 border-l-2 border-gray-300"></div>
                    </div>
                    <a href="./contact.html" class="nav-cta-btn" data-i18n="nav.contact">Contact Sales</a>

                    <!-- Language Toggle - Mobile -->
                    <div class="language-toggle-mobile">
                        <div class="language-toggle">
                            <button id="lang-en-mobile" class="lang-btn" data-lang="en">EN</button>
                            <span class="lang-separator">|</span>
                            <button id="lang-ar-mobile" class="lang-btn" data-lang="ar">العربية</button>
                        </div>
                    </div>
                    <!-- Language Toggle - Desktop -->
                    <div class="language-toggle-desktop">
                        <div class="language-toggle">
                            <button id="lang-en" class="lang-btn" data-lang="en">EN</button>
                            <span class="lang-separator">|</span>
                            <button id="lang-ar" class="lang-btn" data-lang="ar">العربية</button>
                        </div>
                    </div>

                    <!-- Theme Switch -->
                    <div class="theme-switch" id="theme-switch">
                        <i class="ri-moon-line moon-icon"></i>
                        <i class="ri-sun-line sun-icon"></i>
                        <script>
                            (() => {
                                const savedTheme = localStorage.getItem('theme');
                                const preferred = savedTheme || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                                document.documentElement.setAttribute('data-theme', preferred);
                            })();
                        </script>
                    </div>
                </div>
            </nav>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="about-hero pt-32 pb-16">
        <div class="container mx-auto px-4" style="margin-top: 10%;">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-6" data-i18n="about.hero.title">Our Story</h1>
            <p class="text-xl text-white max-w-2xl" data-i18n="about.hero.subtitle">Building the future of business through innovation and automation.</p>
        </div>
    </section>

    <!-- Story Section -->
    <section class="section">
        <div class="container">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="section-title text-left mb-6" data-i18n="about.story.title">Our Journey</h2>
                    <div class="timeline">
                        <div class="timeline-item">
                            <h3 class="text-xl font-semibold mb-2" data-i18n="about.story.2020">2020 - The Beginning</h3>
                            <p class="text-gray-600" data-i18n="about.story.2020Desc">Founded by a group of engineers with a common vision to transform businesses through groundbreaking technology solutions.</p>
                        </div>
                        <div class="timeline-item">
                            <h3 class="text-xl font-semibold mb-2" data-i18n="about.story.2021">2021 - First Major Success</h3>
                            <p class="text-gray-600" data-i18n="about.story.2021Desc">Closed our first deal with a major client and started our first venture in Egypt.</p>
                        </div>
                        <div class="timeline-item">
                            <h3 class="text-xl font-semibold mb-2" data-i18n="about.story.2023">2023 - The Big Shift</h3>
                            <p class="text-gray-600" data-i18n="about.story.2023Desc">Realigned our vision and started focusing on venture building.</p>
                        </div>
                        <div class="timeline-item">
                            <h3 class="text-xl font-semibold mb-2" data-i18n="about.story.2024">2024 - Automation Hub</h3>
                            <p class="text-gray-600" data-i18n="about.story.2024Desc">Adopted the automation mantra, and started providing automation solutions to our clients.</p>
                        </div>
                        <div class="timeline-item">
                            <h3 class="text-xl font-semibold mb-2" data-i18n="about.story.2025">2025 - Today</h3>
                            <p class="text-gray-600" data-i18n="about.story.2025Desc">Expanding our operations and services to the GCC region through our new offices in Sharjah.</p>
                        </div>
                    </div>
                </div>
                <div class="relative">
                    <img src="https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg" alt="Sahla Team" class="rounded-lg shadow-lg">
                    <div class="absolute -bottom-6 -right-6 bg-primary text-white p-6 rounded-lg">
                        <p class="text-2xl font-bold">5+</p>
                        <p>Years of Innovation</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Vision & Mission Section -->
    <section class="section py-20 overflow-hidden" style="background-color: var(--background-color); color: var(--text-color);">
        <div class="container mx-auto px-6 lg:px-12 relative">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <h2 class="text-4xl font-extrabold tracking-tight sm:text-5xl" style="color: var(--primary-color-1);" data-i18n="about.visionMission.title">Vision & Mission</h2>
                <p class="mt-4 text-lg" style="color: var(--text-color-light);" data-i18n="about.visionMission.subtitle">Guided by purpose. Driven by innovation. Built for the region.</p>
            </div>
            <div class="flex flex-col md:flex-row gap-10 items-stretch vision-mission-row ">
                <!-- Vision Card -->
                <div class="flex-1 p-[1px] rounded-3xl shadow-xl transform hover:scale-105 transition-transform duration-300"
                     style="background: var(--gradient-primary); box-shadow: var(--shadow-lg);">
                    <div class="rounded-3xl h-full p-8 relative overflow-hidden" style="background-color: var(--background-color);">
                        <!-- Decorative Circles -->
                        <div class="absolute -right-14 -top-14 w-40 h-40 rounded-full opacity-10" style="background-color: var(--primary-color-1);"></div>
                        <div class="absolute -left-14 -bottom-14 w-32 h-32 rounded-full opacity-10" style="background-color: var(--secondary-color-1);"></div>
                        <div class="relative z-10">
                            <div class="flex items-center mb-6 vision-row !flex-row">
                                <div class="w-12 h-12 rounded-full flex items-center justify-center text-white text-2xl mr-4 vision-icon "
                                     style="background-color: var(--primary-color-1);">
                                    <i class="ri-eye-fill"></i>
                                </div>
                                <h3 class="text-2xl font-bold" style="color: var(--text-color);" data-i18n="about.vision.title">Our Vision</h3>
                            </div>
                            <p class="text-lg leading-relaxed" style="color: var(--text-color-light);" data-i18n="about.vision.desc">
                                To lead the Middle East's tech industry by developing innovative, sustainable solutions that grow into thriving ventures under the Sahla Smart Solutions umbrella.
                            </p>
                        </div>
                    </div>
                </div>
                <!-- Mission Card -->
                <div class="flex-1 p-[1px] rounded-3xl shadow-xl transform hover:scale-105 transition-transform duration-300"
                     style="background: var(--gradient-primary); box-shadow: var(--shadow-lg);">
                    <div class="rounded-3xl h-full p-8 relative overflow-hidden" style="background-color: var(--background-color);">
                        <!-- Decorative Circles -->
                        <div class="absolute -right-14 -bottom-14 w-40 h-40 rounded-full opacity-10" style="background-color: var(--secondary-color-1);"></div>
                        <div class="absolute -left-14 -top-14 w-32 h-32 rounded-full opacity-10" style="background-color: var(--primary-color-1);"></div>
                        <div class="relative z-10">
                            <div class="flex items-center mb-6 mission-row !flex-row">
                                <div class="w-12 h-12 rounded-full flex items-center justify-center text-white text-2xl mr-4 mission-icon"
                                     style="background-color: var(--secondary-color-1);">
                                    <i class="ri-flag-fill"></i>
                                </div>
                                <h3 class="text-2xl font-bold" style="color: var(--text-color);" data-i18n="about.mission.title">Our Mission</h3>
                            </div>
                            <p class="text-lg leading-relaxed" style="color: var(--text-color-light);" data-i18n="about.mission.desc">
                                To deliver advanced, emerging technology solutions that create real value — empowering businesses with clarity and consumers with creativity, driving both personal and professional growth.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Leadership Team Section -->
    <section class="section bg-light">
        <div class="container">
            <div class="text-center mb-16">
                <h2 class="section-title !text-center" data-i18n="about.leadership.title">Our Leadership Team</h2>
                <p class="section-subtitle !text-center" data-i18n="about.leadership.subtitle">Meet the visionaries driving Sahla's success</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mx-auto">
                <!-- CEO -->
                <div class="team-member-card rounded-lg overflow-hidden shadow-md mx-auto" style="background-color: var(--background-color); width: 60%;">
                    <img src="../assets/images/mohamed.jpg" alt="CEO" class="w-full h-64 object-cover" style="object-fit: cover;">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2" style="color: var(--text-color);">Mohamed Abouayana</h3>
                        <p class="text-primary mb-4">Co-CEO</p>
                        <p class="text-gray-600 mb-4">Mechatronics Engineering major with a passion for Business & Enterprise Development.</p>
                        <div class="flex space-x-4">
                            <a href="https://de.linkedin.com/in/m-abouayana" class="text-gray-400 hover:text-primary"><i class="ri-linkedin-fill"></i></a>
                        </div>
                    </div>
                </div>
                <!-- CTO -->
                <div class="team-member-card rounded-lg overflow-hidden shadow-md mx-auto" style="background-color: var(--background-color); width: 60%;">
                    <img src="../assets/images/rou.jpg" alt="CTO" class="w-full h-64 object-cover" style="object-position: top;">
                    <div class="p-6">
                        <h3 class="text-xl font-semibold mb-2" style="color: var(--text-color);">Marwan Rekaby</h3>
                        <p class="text-primary mb-4">Co-CEO & Chief Technology Officer</p>
                        <p class="text-gray-600 mb-4">Technology innovator specializing in AI and automation solutions.</p>
                        <div class="flex space-x-4">
                            <a href="https://www.linkedin.com/in/m-mokhles" class="text-gray-400 hover:text-primary"><i class="ri-linkedin-fill"></i></a>
                            <a href="https://x.com/MaRouaneMokhles" class="text-gray-400 hover:text-primary"><i class="ri-twitter-fill"></i></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- What We Believe Section -->
    <section class="section ">
        <div class="container">
            <div class="text-center mb-16">
                <h2 class="section-title !text-center" data-i18n="about.beliefs.title">What We Believe</h2>
                <p class="section-subtitle !text-center" data-i18n="about.beliefs.subtitle">Our Core Philosophy</p>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Innovation -->
                <div class=" p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300" style="background-color: var(--background-color-light);">
                    <div class="text-primary text-4xl mb-4">
                        <i class="ri-strong-line"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-i18n="about.beliefs.endeavor">Endeavor & Accountability</h3>
                    <p class="text-gray-400" data-i18n="about.beliefs.endeavorDesc">To always give our best and hold ourselves accountable for the results</p>
                </div>
                <!-- Excellence -->
                <div class=" p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300" style="background-color: var(--background-color-light);">
                    <div class="text-primary text-4xl mb-4">
                        <i class="ri-book-open-fill"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-i18n="about.beliefs.learning">Endless Group Learning</h3>
                    <p class="text-gray-400" data-i18n="about.beliefs.learningDesc">We are constantly engaging in both learning and instructing peers about emerging technologies and innovative strategies to maximize their application, aiming to deliver unparalleled value to our clients.</p>
                </div>
                <!-- Collaboration -->
                <div class=" p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300" style="background-color: var(--background-color-light);">
                    <div class="text-primary text-4xl mb-4">
                        <i class="ri-emotion-happy-fill"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-i18n="about.beliefs.ux">Exceptional User Experience</h3>
                    <p class="text-gray-400" data-i18n="about.beliefs.uxDesc">We design intuitive interfaces and seamless interactions that delight users.</p>
                </div>
                <!-- Integrity -->
                <div class=" p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300" style="background-color: var(--background-color-light);">
                    <div class="text-primary text-4xl mb-4">
                        <i class="ri-service-fill"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-i18n="about.beliefs.convenience">Convenience</h3>
                    <p class="text-gray-400" data-i18n="about.beliefs.convenienceDesc">Our solutions have to always have the utmost convenience of its target users in mind.</p>
                </div>
                <!-- Adaptability -->
                <div class=" p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300" style="background-color: var(--background-color-light);">
                    <div class="text-primary text-4xl mb-4">
                        <i class="ri-customer-service-2-fill"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-i18n="about.beliefs.customer">Customer Centricity</h3>
                    <p class="text-gray-400" data-i18n="about.beliefs.customerDesc">We are committed to understanding and serving the needs of both our business clients and individual consumers.</p>
                </div>
                <!-- Impact -->
                <div class=" p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300" style="background-color: var(--background-color-light);">
                    <div class="text-primary text-4xl mb-4">
                        <i class="ri-message-2-fill"></i>
                    </div>
                    <h3 class="text-xl font-semibold mb-3" data-i18n="about.beliefs.communication">Effective Communication</h3>
                    <p class="text-gray-400" data-i18n="about.beliefs.communicationDesc">We strive to create transparent, interactive and effective communication channels that allow us and our clients to do better.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Leadership & Culture Section -->
    <section class="section bg-light">
        <div class="container">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                    <h2 class="section-title text-left mb-6" data-i18n="about.culture.title">Leadership & Culture</h2>
                    <p class="text-gray-400 mb-6" data-i18n="about.culture.desc">At Sahla, we foster a culture of innovation, collaboration, and continuous learning. Our leadership team is committed to creating an environment where creativity thrives and excellence is celebrated.</p>

                    <div class="space-y-6">
                        <div class="flex items-start ltr:flex-row rtl:flex-row-reverse !flex-row">
                            <div class="text-primary text-2xl mr-4 rtl:ml-4 rtl:mr-0">
                                <i class="ri-user-heart-line"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold mb-2" data-i18n="about.culture.people">People-First Approach</h3>
                                <p class="text-gray-400" data-i18n="about.culture.peopleDesc">We believe that our people are our greatest asset. We invest in their growth, well-being, and professional development.</p>
                            </div>
                        </div>

                        <div class="flex items-start ltr:flex-row rtl:flex-row-reverse !flex-row">
                            <div class="text-primary text-2xl mr-4 rtl:ml-4 rtl:mr-0">
                                <i class="ri-lightbulb-flash-line"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold mb-2" data-i18n="about.culture.excite">Excite and inspire</h3>
                                <p class="text-gray-400" data-i18n="about.culture.exciteDesc">We believe our work is rewarding, exciting and we inspire our people to bring their best selves to work.</p>
                            </div>
                        </div>

                        <div class="flex items-start ltr:flex-row rtl:flex-row-reverse !flex-row">
                            <div class="text-primary text-2xl mr-4 rtl:ml-4 rtl:mr-0">
                                <i class="ri-rocket-line"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold mb-2" data-i18n="about.culture.growth">Continuous Growth</h3>
                                <p class="text-gray-400" data-i18n="about.culture.growthDesc">We encourage continuous learning and personal growth, providing opportunities for skill development and career advancement.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="relative">
                    <img src="https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg" alt="Sahla Culture" class="rounded-lg shadow-lg">
                    <div class="absolute -bottom-6 -right-6 bg-primary text-white p-6 rounded-lg">
                        <p class="text-2xl font-bold text-white">100%</p>
                        <p class="text-white">Team Satisfaction</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="section bg-primary py-20">
        <div class="container">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-white mb-4" data-i18n="about.cta.title">Join Us on Our Journey</h2>
                <p class="text-xl text-white max-w-2xl mx-auto" data-i18n="about.cta.desc">Whether you're looking to partner with us or join our team, we'd love to hear from you.</p>
            </div>
            <div class="flex flex-col sm:flex-row justify-center gap-6">
                <a href="partners.html" class="bg-white text-primary hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-300 flex items-center justify-center" data-i18n="about.cta.partner">
                    <i class="ri-shake-hands-fill mr-2"></i> Become a Partner
                </a>
                <a href="careers.html" class="bg-transparent border-2 border-white text-white hover:bg-white hover:text-primary px-8 py-3 rounded-lg font-semibold text-lg transition-colors duration-300 flex items-center justify-center" data-i18n="about.cta.join">
                    <i class="ri-user-add-fill mr-2"></i> Join the Team
                </a>
            </div>
        </div>
    </section>

    <!-- f -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Company Info Column -->
                <div class="footer-info">
                    <a href="../index.html" class="footer-logo">
                        <img src="../assets/images/Negative Horizontal Version.svg" alt="Sahla Smart Solutions" class="logo-img" id="foot-logo">
                    </a>
                    <p class="footer-description" data-i18n="footer.description">Empowering businesses through innovative venture building and automation solutions in the MENA region.</p>
                    <div class="footer-social">
                        <a href="https://eg.linkedin.com/company/sahla-solutions" target="_blank" rel="noopener noreferrer"><i class="ri-linkedin-fill"></i></a>
                        <a href="https://x.com/sahlasolutions" target="_blank" rel="noopener noreferrer"><i class="ri-twitter-fill"></i></a>
                        <a href="https://www.facebook.com/SahlaSmartSolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-facebook-fill"></i></a>
                        <a href="https://www.instagram.com/sahlasolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-instagram-fill"></i></a>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div class="footer-links-column">
                    <h4 data-i18n="footer.quickLinks">Quick Links</h4>
                    <ul class="footer-links-list">
                        <li><a href="../index.html" data-i18n="footer.home">Home</a></li>
                        <li><a href="./about.html" data-i18n="footer.aboutUs">About Us</a></li>
                        <li><a href="./ventures.html" data-i18n="footer.ourVentures">Our Ventures</a></li>
                        <li><a href="./partners.html" data-i18n="nav.partners">Partners</a></li>
                        <li><a href="./careers.html" data-i18n="nav.careers">Careers</a></li>
                        <li><a href="./contact.html" data-i18n="footer.contactUs">Contact Us</a></li>
                    </ul>
                </div>

                <!-- Services Column -->
                <div class="footer-links-column">
                    <h4 data-i18n="footer.services">Services</h4>
                    <ul class="footer-links-list">
                        <li><a href="./process-automation.html" data-i18n="nav.processAutomation">Process Automation</a></li>
                        <li><a href="./technology-consulting.html" data-i18n="nav.technologyConsulting">Technology Consulting</a></li>
                    </ul>
                </div>

                <!-- Newsletter Column -->
                <div class="footer-newsletter">
                    <h4 data-i18n="footer.newsletter">Newsletter</h4>

                    <form class="mb-4">
                        <div class="flex !flex-row">
                            <!-- <input type="email" placeholder="Your email" class="w-full px-4 py-2 bg-gray-800 border-none text-white text-sm rounded-l"> -->
                            <a href="javascript:void(0)" class="ml-onclick-form" onclick="ml('show', 'gturk0', true)" style="
                                display: inline-block;
                                padding: 8px 16px;
                                background-color: var(--primary-color-1);
                                color: white;
                                font-size: 16px;
                                font-family: sans-serif;
                                border: none;
                                border-radius: 6px;
                                text-decoration: none;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                                " onmouseover="this.style.backgroundColor='var(--primary-color-2)'" onmouseout="this.style.backgroundColor= 'var(--primary-color-3)'" data-i18n="footer.subscribeNewsletter">
                                Subscribe to Newsletter
                                </a>
                        </div>
                    </form>
                    <p data-i18n="footer.newsletterDescription">Subscribe to our newsletter to stay updated with our latest news and insights.</p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0" data-i18n="footer.copyright">© 2025 Sahla Smart Solutions. All rights reserved.</p>
                    <div class="flex space-x-6">
                        <a href="./pages/privacy-policy.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.privacyPolicy">Privacy Policy</a>
                        <a href="./pages/terms-of-service.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.termsOfService">Terms of Service</a>
                        <a href="./pages/cookies-policy.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.cookiesPolicy">Cookies Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Theme switch functionality
        const themeSwitch = document.getElementById('theme-switch');
        const html = document.documentElement;
        const navLogo = document.getElementById('nav-logo');
        const footLogo = document.getElementById('foot-logo');

        // Function to set and save theme
        const setTheme = (theme) => {
            html.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            themeSwitch.classList.toggle('dark', theme === 'dark');

            // Update logo based on theme
            if (theme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        };

        // Set the correct theme class on icon container and logo after load
        document.addEventListener('DOMContentLoaded', () => {
            const currentTheme = html.getAttribute('data-theme');
            themeSwitch.classList.toggle('dark', currentTheme === 'dark');

            // Set initial logo based on theme
            if (currentTheme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        });

        // Toggle theme on click
        themeSwitch.addEventListener('click', () => {
            const isCurrentlyDark = html.getAttribute('data-theme') === 'dark';
            const newTheme = isCurrentlyDark ? 'light' : 'dark';
            setTheme(newTheme);
        });

        // Watch for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                setTheme(e.matches ? 'dark' : 'light');
            }
        });

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navLinks = document.querySelector('.nav-links');

        mobileMenuToggle.addEventListener('change', function() {
            if (this.checked) {
                navLinks.style.display = 'flex';
            } else {
                navLinks.style.display = '';
            }
        });

        // Submenu toggle
        function toggleSubmenu(id) {
            const submenu = document.getElementById(id);
            submenu.classList.toggle('hidden');
        }

        // Custom checkbox toggle
        function toggleCheckbox(checkbox) {
            checkbox.classList.toggle('checked');
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                if (targetId === '#') return;

                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    const headerHeight = 80; // Approximate header height
                    const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset - headerHeight;

                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });

                    // Close mobile menu if open
                    if (!mobileMenuToggle.checked) {
                        mobileMenuToggle.checked = false;
                        navLinks.style.display = '';
                    }
                }
            });
        });

        // Sticky header effect
        window.addEventListener('scroll', () => {
            const header = document.querySelector('header');
            if (window.scrollY > 50) {
                header.classList.add('shadow-md');
            } else {
                header.classList.remove('shadow-md');
            }
        });
    </script>

    <!-- i18n Implementation -->
    <script src="../assets/js/i18n.js"></script>
</body>

</html>