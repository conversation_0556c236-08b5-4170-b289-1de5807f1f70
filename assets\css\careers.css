/* Careers Page Styles */
.bg-dark {
    background-color: #111827;
    color: white;
}

/* Career Section */
.career-section {
    padding: 5rem 0 3rem;
}

.career-container {
    background-color: #1f2937;
    border-radius: 0.75rem;
    padding: 2.5rem;
    box-shadow: var(--shadow-lg);
    margin-top: 1.5rem;
}

.career-title {
    color: white;
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.career-subtitle {
    color: #9ca3af;
    margin-bottom: 2rem;
}

/* Career Filters */
.career-filters {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 2rem;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    background-color: #374151;
    border: none;
    border-radius: 0.375rem;
    color: white;
    font-size: 0.875rem;
}

.search-input::placeholder {
    color: #9ca3af;
}

.search-input:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.search-box i {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 1rem;
}

.filter-container {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.filter-dropdown {
    position: relative;
}

.filter-dropdown-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background-color: #374151;
    border: none;
    border-radius: 0.375rem;
    color: white;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.filter-dropdown-btn:hover {
    background-color: #4b5563;
}

.filter-dropdown-btn i {
    font-size: 1.25rem;
}

/* Job Listings */
.job-listings {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.job-card {
    background-color: #111827;
    border-radius: 0.5rem;
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.job-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
}

.job-details {
    flex: 1;
}

.job-details h3 {
    color: white;
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.job-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    color: #9ca3af;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.job-meta span {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.job-meta i {
    font-size: 1rem;
    color: var(--primary-color-1);
}

.job-description {
    color: #d1d5db;
    font-size: 0.875rem;
    line-height: 1.5;
}

.job-action {
    flex-shrink: 0;
}

.view-all {
    color: var(--primary-color-1);
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.3s ease;
}

.view-all:hover {
    color: var(--primary-color-2);
}

.view-all i {
    transition: transform 0.3s ease;
}

.view-all:hover i {
    transform: translateX(4px);
}

/* Benefits Section */
.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.benefit-card {
    background-color: white;
    border-radius: 0.5rem;
    padding: 2rem;
    box-shadow: var(--shadow-md);
    transition: transform 0.3s ease;
    text-align: center;
}

.benefit-card:hover {
    transform: translateY(-5px);
}

.benefit-icon {
    width: 3.5rem;
    height: 3.5rem;
    background-color: rgba(37, 99, 235, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.benefit-icon i {
    font-size: 1.75rem;
    color: var(--primary-color-1);
}

.benefit-card h3 {
    margin-bottom: 0.75rem;
    color: var(--text-color);
    font-size: 1.25rem;
}

.benefit-card p {
    color: var(--text-color-light);
    font-size: 0.9rem;
}

/* Culture Section */
.culture-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    margin-top: 3rem;
}

.culture-image img {
    width: 100%;
    height: auto;
    border-radius: 0.5rem;
    box-shadow: var(--shadow-lg);
}

.culture-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.culture-value h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.culture-value h3 i {
    color: var(--primary-color-1);
    font-size: 1.25rem;
}

.culture-value p {
    color: var(--text-color-light);
    padding-left: 1.75rem;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .job-card {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .job-action {
        width: 100%;
        margin-top: 1rem;
    }
    
    .job-action .btn {
        width: 100%;
        text-align: center;
    }
    
    .culture-grid {
        grid-template-columns: 1fr;
    }
    
    .career-container {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .career-filters {
        flex-direction: column;
    }
    
    .filter-container {
        width: 100%;
    }
    
    .filter-dropdown,
    .filter-dropdown-btn {
        width: 100%;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
    }
} 