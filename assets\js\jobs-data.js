const jobsData = {
    departments: [
        { id: 'all', name: 'All Departments' },
        { id: 'venture-building', name: 'Venture Building' },
        { id: 'product', name: 'Product' },
        { id: 'design', name: 'Design' },
        { id: 'marketing', name: 'Marketing' },
        { id: 'engineering', name: 'Engineering' },
        { id: 'automation', name: 'Automation' },
        { id: 'finance', name: 'Finance' }
    ],

    locations: [
        { id: 'all', name: 'All Locations' },
        { id: 'alexandria', name: 'Alexandria, Egypt' },
        { id: 'cairo', name: 'Cairo, Egypt' },
        { id: 'dubai', name: 'Dubai, UAE' },
        { id: 'riyadh', name: 'Riyadh, KSA' },
        { id: 'remote', name: 'Remote (MENA Region)' }
    ],

    jobs: [

        {
            id: 1,
            title: 'Senior Product Manager',
            department: 'product',
            location: 'remote',
            type: 'Full-time',
            description: 'Own the product strategy and roadmap while leading cross-functional teams to deliver high-impact solutions.',
            requirements: [
                '5+ years of experience in product management',
                'Proven track record of launching and scaling tech products',
                'Strong analytical, communication, and leadership skills',
                'Familiarity with agile methodologies and product analytics'
            ],
            responsibilities: [
                'Define product roadmap and success metrics',
                'Conduct market research and user analysis',
                'Work with cross-functional teams to build and launch products',
                'Prioritize features based on impact and feasibility'
            ]
        },
        {
            id: 2,
            title: 'UX/UI Designer',
            department: 'design',
            location: 'remote',
            type: 'Full-time',
            description: 'Design seamless, user-friendly experiences and interfaces across our digital products.',
            requirements: [
                '2–4 years of experience in UX/UI design',
                'Strong portfolio showcasing digital product work',
                'Proficiency in Figma, Adobe XD, or Sketch',
                'Understanding of responsive and accessible design principles'
            ],
            responsibilities: [
                'Create wireframes, prototypes, and visual designs',
                'Conduct user research and usability testing',
                'Collaborate with developers and product managers',
                'Maintain and evolve design systems'
            ]
        },
        {
            id: 3,
            title: 'Junior Software Developer',
            department: 'engineering',
            location: 'remote',
            type: 'Full-time',
            description: 'Support the development and deployment of scalable applications within an agile team.',
            requirements: [
                '0–2 years of experience in software development',
                'Proficiency in Python, JavaScript, or similar languages',
                'Understanding of REST APIs and Git workflows',
                'Eagerness to learn and contribute in a startup environment'
            ],
            responsibilities: [
                'Write and maintain clean, efficient code',
                'Support testing, debugging, and documentation',
                'Participate in agile development processes',
                'Work with APIs, cloud services, and databases'
            ]
        },
        {
            id: 4,
            title: 'Financial Accountant',
            department: 'finance',
            location: 'remote',
            type: 'Full-time',
            description: 'Manage financial records, ensure regulatory compliance, and support financial planning and reporting.',
            requirements: [
                '3+ years of experience in accounting or financial control',
                'Bachelor’s degree in Accounting or Finance',
                'Proficiency in accounting software (e.g., Zoho Books, QuickBooks)',
                'Knowledge of tax regulations and financial reporting standards'
            ],
            responsibilities: [
                'Maintain accurate financial records and ledgers',
                'Prepare monthly and annual reports',
                'Ensure compliance with local tax laws',
                'Support budgeting and financial planning processes'
            ]
        },
        {
            id: 5,
            title: 'Marketing Manager',
            department: 'marketing',
            location: 'remote',
            type: 'Full-time',
            description: 'Lead brand and growth marketing strategies to increase visibility, engagement, and lead generation.',
            requirements: [
                '4+ years of marketing experience in tech or startup environments',
                'Strong understanding of digital channels and tools',
                'Experience with performance metrics and campaign optimization',
                'Excellent project management and communication skills'
            ],
            responsibilities: [
                'Develop and execute multi-channel marketing strategies',
                'Manage digital campaigns and content creation',
                'Analyze performance data and optimize accordingly',
                'Coordinate with internal and external stakeholders'
            ]
        },
        {
            id: 6,
            title: 'Automation Engineer',
            department: 'automation',
            location: 'remote',
            type: 'Full-time',
            description: 'Design and implement automation solutions for enterprise clients across various industries.',
            requirements: [
                '1+ years of experience in process automation',
                'Strong programming skills (Python, JavaScript)',
                'Experience with RPA tools and platforms',
                'Understanding of business processes'
            ],
            responsibilities: [
                'Design automation solutions',
                'Develop and implement automation workflows',
                'Integrate with existing systems',
                'Train clients on automation tools'
            ]
        }
    ]


}; 