<!DOCTYPE html>
<html lang="en">
<head>     <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico?v=2">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Sahla Smart Solutions</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>tailwind.config={theme:{extend:{colors:{primary:'#0056b3',secondary:'#4dabf7'},borderRadius:{'none':'0px','sm':'4px',DEFAULT:'8px','md':'12px','lg':'16px','xl':'20px','2xl':'24px','3xl':'32px','full':'9999px','button':'8px'}}}}</script>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/navigation.css">

    <!-- Dynamic Font Loading -->
    <link id="google-fonts" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- i18next Core and HTTP Backend -->
    <script src="https://unpkg.com/i18next@23.7.16/dist/umd/i18next.min.js"></script>
    <script src="https://unpkg.com/i18next-http-backend@2.4.2/i18nextHttpBackend.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/validator@13.9.0/validator.min.js"></script>
    <style>
        .contact-hero {
            background-image: linear-gradient(to bottom, #111827d8, #22267a8f), url('https://images.pexels.com/photos/8386440/pexels-photo-8386440.jpeg');
            background-size: cover;
            background-position: center;
            padding: 8rem 0 4rem;
        }

        .contact-form {
            background: var(--bg-light);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-color);
        }

        .required-field::after {
            content: " *";
            color: #e53e3e;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            background: var(--bg-color);
            color: var(--text-color);
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .form-textarea {
            min-height: 150px;
            resize: vertical;
        }

        .contact-info-card {
            background: var(--bg-light);
            border-radius: 1rem;
            padding: 2rem;
            height: 100%;
            transition: transform 0.3s ease;
        }

        .contact-info-card:hover {
            transform: translateY(-5px);
        }

        .contact-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            color: white;
            border-radius: 12px;
            margin-bottom: 1rem;
        }

        .success-message {
            display: none;
            background: #10B981;
            color: white;
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .error-message {
            color: #e53e3e;
            font-size: 0.875rem;
            margin-top: 0.25rem;
            display: none;
        }

        .border-red-500 {
            border-color: #e53e3e !important;
        }

        .custom-notification {
            position: fixed;
            top: 32px;
            left: 50%;
            transform: translateX(-50%);
            min-width: 320px;
            max-width: 90vw;
            z-index: 9999;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            color: var(--background-color);
            box-shadow: 0 4px 16px rgba(0,0,0,0.08);
            display: none;
            align-items: center;
            gap: 0.75rem;
        }
        .custom-notification.success {
            background: var(--secondary-color-1);
            color: #fff;
        }
        .custom-notification.error {
            background: #e53e3e;
            color: #fff;
        }
        .custom-notification .close-btn {
            background: none;
            border: none;
            color: inherit;
            font-size: 1.25rem;
            margin-left: 1rem;
            cursor: pointer;
        }
        @media (max-width: 480px) {
            .custom-notification {
                min-width: 180px;
                font-size: 0.95rem;
                padding: 0.75rem 1rem;
            }
        }
       .service-menu {
            background-color: var(--background-color);
        }

        /* Language Toggle Styles */
        .language-toggle-desktop {
            display: flex;
            align-items: center;
            margin-left: 1rem;
        }

        .language-toggle-mobile {
            display: none;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .language-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.25rem 0.5rem;
            background: var(--background-color);
            transition: all 0.3s ease;
        }

        .lang-btn {
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            background: transparent;
            cursor: pointer;
            color: var(--text-color);
        }

        .lang-btn:hover {
            background-color: var(--primary-color-1);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        .lang-btn.active {
            background-color: var(--primary-color-1);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .lang-btn.inactive {
            color: var(--text-color-light);
            background: transparent;
        }

        .lang-btn.inactive:hover {
            background-color: var(--background-color-light);
            color: var(--text-color);
        }

        .lang-separator {
            color: var(--text-color-light);
            font-weight: 300;
            transition: color 0.3s ease;
        }

        /* Navigation CTA Button */
        .nav-cta-btn {
            background: var(--primary-color-1);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-cta-btn:hover {
            background-color: var(--primary-color-2);
            transform: translateY(-1px);
        }

        .nav-divider {
            display: flex;
            align-items: center;
            padding: 0 1rem;
        }

        /* Dark mode specific adjustments */
        [data-theme="dark"] .language-toggle {
            border-color: var(--border-color);
            background: var(--background-color);
        }

        [data-theme="dark"] .lang-btn {
            color: var(--text-color);
        }

        [data-theme="dark"] .lang-btn:hover {
            background-color: var(--primary-color-1);
            color: white;
        }

        [data-theme="dark"] .lang-btn.active {
            background-color: var(--primary-color-1);
            color: white;
        }

        [data-theme="dark"] .lang-btn.inactive {
            color: var(--text-color-light);
            background: transparent;
        }

        [data-theme="dark"] .lang-btn.inactive:hover {
            background-color: var(--background-color-light);
            color: var(--text-color);
        }

        [data-theme="dark"] .lang-separator {
            color: var(--text-color-light);
        }

        /* RTL Support */
        [dir="rtl"] {
            text-align: right;
        }

        /* Navigation RTL fixes */
        [dir="rtl"] .nav {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .nav-links {
            flex-direction: row-reverse;
            gap: 2rem;
        }

        [dir="rtl"] .nav-logo {
            margin-left: 0;
            margin-right: 0;
        }

        [dir="rtl"] .nav-links .dropdown {
            text-align: right;
        }

        [dir="rtl"] .dropdown-content {
            left: auto;
            right: 0;
            text-align: right;
        }

        [dir="rtl"] .dropdown-link {
            text-align: right;
        }

        [dir="rtl"] .nav-divider {
            padding: 0 1rem;
        }

        [dir="rtl"] .language-toggle-desktop {
            margin-left: 0;
            margin-right: 1rem;
        }

        [dir="rtl"] .language-toggle {
            flex-direction: row-reverse;
        }

        /* Mobile RTL Navigation */
        [dir="rtl"] .mobile-menu-button {
            order: -1;
        }

        [dir="rtl"] .nav-links {
            text-align: right;
        }

        [dir="rtl"] .nav-link {
            text-align: right;
        }

        [dir="rtl"] .nav-cta-btn {
            text-align: center;
        }

  

        [dir="rtl"] .space-x-4 > * + * {
            margin-left: 0;
            margin-right: 1rem;
        }

        /* Mobile Navigation Responsive Styles */
        @media (max-width: 768px) {
            .language-toggle-desktop {
                display: none;
            }

            .language-toggle-mobile {
                display: flex;
                justify-content: center;
            }

            .nav-divider {
                display: none;
            }

            .nav-cta-btn {
                width: 100%;
                text-align: center;
                margin-top: 0.5rem;
            }

            /* RTL Mobile Fixes */
            [dir="rtl"] .nav-links {
                text-align: right;
            }

            [dir="rtl"] .nav-link {
                text-align: right;
            }

            [dir="rtl"] .dropdown-link {
                text-align: right;
            }

            [dir="rtl"] .nav-cta-btn {
                text-align: center;
            }
        }

        /* Arabic Font Support */
        .lang-ar {
            font-family: 'Cairo', sans-serif;
        }

        .lang-en {
            font-family: 'Inter', sans-serif;
        }
    </style>
</head>
<body class="bg-white">
    <div id="custom-notification" class="custom-notification" role="alert">
        <span id="custom-notification-message"></span>
        <button class="close-btn" onclick="hideNotification()" aria-label="Close notification">&times;</button>
    </div>
    <!-- Header & Navigation -->
   <header class="header">
    <div class="container">
      <nav class="nav">
        <a href="../index.html" class="nav-logo">
          <img src="../assets/images/Main Horizontal Version.svg" alt="Sahla Logo" class="logo-img" id="nav-logo">
        </a>
        <input type="checkbox" id="mobile-menu-toggle" class="mobile-menu-toggle">
        <label for="mobile-menu-toggle" class="mobile-menu-button">
          <span></span>
          <span></span>
          <span></span>
        </label>
        <div class="nav-links">
          <div class="dropdown">
            <a href="#services" class="nav-link" data-i18n="nav.services">Services</a>
            <div class="dropdown-content">
              <a href="./process-automation.html" class="dropdown-link" data-i18n="nav.processAutomation">Process Automation</a>
              <a href="./technology-consulting.html" class="dropdown-link" data-i18n="nav.technologyConsulting">Technology Consulting</a>
            </div>
          </div>
          <a href="./ventures.html" class="nav-link" data-i18n="nav.ventures">Our Ventures</a>
          <a href="./partners.html" class="nav-link" data-i18n="nav.partners">Partners</a>
          <a href="./careers.html" class="nav-link" data-i18n="nav.careers">Careers</a>
          <a href="./blog.html" class="nav-link" data-i18n="nav.blog">Blog</a>
          <a href="./about.html" class="nav-link" data-i18n="nav.about">About us</a>
          <div class="nav-divider">
            <div class="h-6 border-l-2 border-gray-300"></div>
          </div>
          <a href="./contact.html" class="nav-cta-btn" data-i18n="nav.contact">Contact Sales</a>

          <!-- Language Toggle - Mobile -->
          <div class="language-toggle-mobile">
            <div class="language-toggle">
              <button id="lang-en-mobile" class="lang-btn" data-lang="en">EN</button>
              <span class="lang-separator">|</span>
              <button id="lang-ar-mobile" class="lang-btn" data-lang="ar">العربية</button>
            </div>
          </div>
          <!-- Language Toggle - Desktop -->
          <div class="language-toggle-desktop">
            <div class="language-toggle">
              <button id="lang-en" class="lang-btn" data-lang="en">EN</button>
              <span class="lang-separator">|</span>
              <button id="lang-ar" class="lang-btn" data-lang="ar">العربية</button>
            </div>
          </div>

          <!-- Theme Switch -->
          <div class="theme-switch" id="theme-switch">
            <i class="ri-moon-line moon-icon"></i>
            <i class="ri-sun-line sun-icon"></i>
             <script>
                            (() => {
                                const savedTheme = localStorage.getItem('theme');
                                const preferred = savedTheme || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                                document.documentElement.setAttribute('data-theme', preferred);
                            })();
                        </script>
          </div>
        </div>
      </nav>
    </div>
  </header>

    <!-- Contact Hero Section -->
    <section class="contact-hero">
        <div class="container">
            <div class="text-center text-white">
                <h1 class="h1 mb-4 flex" style="margin-top: 10%;" data-i18n="contact.title">Get in Touch</h1>
                <p class="text-xl max-w-2xl" style="text-align: start; color: #e5e7eb;" data-i18n="contact.subtitle">Ready to transform your business? Let's discuss how we can help you achieve your goals through venture building or automation solutions.</p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="section">
        <div class="container">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Contact Information -->
                <div class="lg:col-span-1">
                    <div class="contact-info-card">
                        <h2 class="h2 mb-6" data-i18n="contact.contactInformation">Contact Information</h2>
                        <div class="space-y-6">
                            <div>
                                <div class="flex items-center justify-content-start mb-2 h3 space-x-2">
                                     <i class="ri-map-pin-line text-xl !font-normal"></i>
                                <h3 class="mt-1" data-i18n="contact.ourLocation">Our Location</h3>
                                </div>
                                <p class="text-gray-400" data-i18n="contact.address">7 Abou Rafea, Abu an Nawatir, <br> Sidi Gaber, Alexandria Governorate</p>
                            </div>
                            <div>
                                <div class="flex items-center justify-content-start mb-2 h3 space-x-2">
                                     <i class="ri-mail-line text-xl !font-normal"></i>
                                <h3 class="mt-0.5" data-i18n="contact.emailUs">Email Us</h3>
                                </div>
                                <p class="text-gray-400"><a href="mailto:<EMAIL>" class="__cf_email__" data-cfemail="c3aaada5ac83b0a2abafa2b0acafb6b7aaacadb0eda0acae" data-i18n="contact.emailLink">Click here to Email Us</a></p>
                            </div>
                            <div>
                                  <div class="flex items-center justify-content-start mb-2 h3 space-x-2">
                                     <i class="ri-phone-line text-xl !font-normal"></i>
                                <h3 class="mt-0.5" data-i18n="contact.callUs">Call Us</h3>
                                </div>
                                <p class="text-gray-400" data-i18n="contact.phoneNumber">+20 1553869950</p>
                            </div>
                            <div>
                                     <div class="flex items-center justify-content-start mb-2 h3 space-x-2">
                                     <i class="ri-time-line text-xl !font-normal"></i>
                                <h3 class="mt-0.5" data-i18n="contact.businessHours">Business Hours</h3>
                                </div>
                                <p class="text-gray-400" data-i18n="contact.hours">Saturday - Thursday: 8:00 AM - 6:00 PM<br>Friday: Closed</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="lg:col-span-2">
                    <p class="text-gray-400" data-i18n="contact.formDescription">
                        Please provide detailed information about your inquiry in the form below. A member of our specialized team will contact you via email or phone within 1-2 business days to discuss your needs and how we can help.
                    </p>
                    <span></span>
                    <div class="contact-form">

                        <form id="contact-form" onsubmit="return handleSubmit(event)">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="form-group">
                                    <label for="full-name" class="form-label required-field" data-i18n="forms.fullName">Full Name</label>
                                    <input type="text" id="full-name" name="full-name" class="form-input" required
                                        data-i18n-placeholder="forms.fullName" placeholder="John Doe" pattern="[A-Za-z\s]{2,50}"
                                        title="Please enter a valid name (2-50 characters, letters and spaces only)">
                                    <div class="error-message">Please enter your full name</div>
                                </div>
                                <div class="form-group">
                                    <label for="email" class="form-label required-field" data-i18n="forms.emailAddress">Email Address</label>
                                    <input type="email" id="email" name="email" class="form-input" required
                                        data-i18n-placeholder="forms.emailAddress" placeholder="<EMAIL>">
                                    <div class="error-message">Please enter a valid email address</div>
                                </div>
                                <div class="form-group">
                                    <label for="phone" class="form-label" data-i18n="forms.phoneNumber">Phone Number</label>
                                    <input type="tel" id="phone" name="phone" class="form-input"
                                        data-i18n-placeholder="forms.phoneNumber" placeholder="+20 ************" pattern="[\+\d\s\-\(\)]{10,20}"
                                        title="Please enter a valid phone number">
                                    <div class="error-message">Please enter a valid phone number</div>
                                </div>
                                <div class="form-group">
                                    <label for="company" class="form-label" data-i18n="forms.companyName">Company Name</label>
                                    <input type="text" id="company" name="company" class="form-input"
                                        data-i18n-placeholder="forms.companyName" placeholder="Your Company">
                                    <div class="error-message">Please enter your company name</div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="service" class="form-label required-field" data-i18n="forms.serviceInterest">Service Interest</label>
                                <select id="service" name="service" class="form-input" required>
                                    <option value="" class="service-menu" data-i18n="forms.selectService">Select a service</option>
                                    <option value="general-inquiry" class="service-menu" data-i18n="forms.generalInquiry">General Inquiry</option>
                                    <option value="automation-service" class="service-menu" data-i18n="forms.bookAutomation">Book an Automation Service Session</option>
                                    <option value="digital-transformation" class="service-menu" data-i18n="forms.digitalTransformation">Digital Transformation</option>
                                    <option value="technology-consulting" class="service-menu" data-i18n="forms.technologyConsulting">Technology Consulting</option>
                                </select>
                                <div class="error-message">Please select a service</div>
                            </div>
                            <div class="form-group">
                                <label class="form-label required-field" data-i18n="forms.contactPreference">Contact Preference</label>
                                <div class="flex gap-4 mt-2 !flex-row">
                                    <div class="flex items-center">
                                        <input type="radio" id="prefer-email" name="contact-preference" value="email" class="mr-3 w-5 h-5 text-primary border-2 border-gray-300 focus:ring-2 focus:ring-primary" checked>
                                        <label for="prefer-email" class="text-base font-medium text-gray-700 cursor-pointer" data-i18n="forms.email">Email</label>
                                    </div>
                                    <div class="flex items-center">
                                        <input type="radio" id="prefer-phone" name="contact-preference" value="phone" class="mr-3 w-5 h-5 text-primary border-2 border-gray-300 focus:ring-2 focus:ring-primary">
                                        <label for="prefer-phone" class="text-base font-medium text-gray-700 cursor-pointer" data-i18n="forms.phone">Phone</label>
                                    </div>
                                </div>
                            </div>
                            <div id="preferred-hours" class="form-group hidden">
                                <label for="call-time" class="form-label">Preferred Call Time</label>
                                <select id="call-time" name="call-time" class="form-input">
                                    <option value="">Select preferred time</option>
                                    <option value="8-10">8:00 AM - 10:00 AM GMT+2</option>
                                    <option value="10-12">10:00 AM - 12:00 PM GMT+2</option>
                                    <option value="12-2">12:00 PM - 2:00 PM GMT+2</option>
                                    <option value="2-4">2:00 PM - 4:00 PM GMT+2</option>
                                    <option value="4-6">4:00 PM - 6:00 PM GMT+2</option>
                                </select>
                                <div class="error-message">Please select a preferred call time</div>
                            </div>
                            <script data-cfasync="false" src="/cdn-cgi/scripts/5c5dd728/cloudflare-static/email-decode.min.js"></script><script>
                                const phonePreference = document.getElementById('prefer-phone');
                                const emailPreference = document.getElementById('prefer-email');
                                const preferredHours = document.getElementById('preferred-hours');

                                phonePreference.addEventListener('change', function() {
                                    if (this.checked) {
                                        preferredHours.classList.remove('hidden');
                                    }
                                });

                                emailPreference.addEventListener('change', function() {
                                    if (this.checked) {
                                        preferredHours.classList.add('hidden');
                                    }
                                });
                            </script>
                            <div class="form-group">
                                <label for="message" class="form-label required-field">Message</label>
                                <textarea id="message" name="message" class="form-input form-textarea" required
                                    placeholder="Tell us about your project or inquiry..."></textarea>
                                <div class="error-message">Please provide a message</div>
                            </div>
                            <button type="submit" class="btn btn-primary w-full" id="form-submit-button" data-i18n="forms.sendMessage">Send Message</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

<!-- f -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Company Info Column -->
                <div class="footer-info">
                    <a href="../index.html" class="footer-logo">
                        <img src="../assets/images/Negative Horizontal Version.svg" alt="Sahla Smart Solutions" class="logo-img" id="foot-logo">
                    </a>
                    <p class="footer-description" data-i18n="footer.description">Empowering businesses through innovative venture building and automation solutions in the MENA region.</p>
                    <div class="footer-social">
                        <a href="https://eg.linkedin.com/company/sahla-solutions" target="_blank" rel="noopener noreferrer"><i class="ri-linkedin-fill"></i></a>
                        <a href="https://x.com/sahlasolutions" target="_blank" rel="noopener noreferrer"><i class="ri-twitter-fill"></i></a>
                        <a href="https://www.facebook.com/SahlaSmartSolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-facebook-fill"></i></a>
                        <a href="https://www.instagram.com/sahlasolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-instagram-fill"></i></a>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div class="footer-links-column">
                    <h4 data-i18n="footer.quickLinks">Quick Links</h4>
                    <ul class="footer-links-list">
                        <li><a href="../index.html" data-i18n="footer.home">Home</a></li>
                        <li><a href="./about.html" data-i18n="footer.aboutUs">About Us</a></li>
                        <li><a href="./ventures.html" data-i18n="footer.ourVentures">Our Ventures</a></li>
                        <li><a href="./partners.html" data-i18n="nav.partners">Partners</a></li>
                        <li><a href="./careers.html" data-i18n="nav.careers">Careers</a></li>
                        <li><a href="./contact.html" data-i18n="footer.contactUs">Contact Us</a></li>
                    </ul>
                </div>

                <!-- Services Column -->
                <div class="footer-links-column">
                    <h4 data-i18n="footer.services">Services</h4>
                    <ul class="footer-links-list">
                        <li><a href="./process-automation.html" data-i18n="nav.processAutomation">Process Automation</a></li>
                        <li><a href="./technology-consulting.html" data-i18n="nav.technologyConsulting">Technology Consulting</a></li>
                    </ul>
                </div>

                <!-- Newsletter Column -->
                <div class="footer-newsletter">
                    <h4 data-i18n="footer.newsletter">Newsletter</h4>

                    <form class="mb-4">
                        <div class="flex">
                            <!-- <input type="email" placeholder="Your email" class="w-full px-4 py-2 bg-gray-800 border-none text-white text-sm rounded-l"> -->
                            <a href="javascript:void(0)" class="ml-onclick-form" onclick="ml('show', 'gturk0', true)" style="
                                display: inline-block;
                                padding: 8px 16px;
                                background-color: var(--primary-color-1);
                                color: white;
                                font-size: 16px;
                                font-family: sans-serif;
                                border: none;
                                border-radius: 6px;
                                text-decoration: none;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                                " onmouseover="this.style.backgroundColor='var(--primary-color-2)'" onmouseout="this.style.backgroundColor= 'var(--primary-color-3)'" data-i18n="footer.subscribeNewsletter">
                                Subscribe to Newsletter
                                </a>
                        </div>
                    </form>
                    <p data-i18n="footer.newsletterDescription">Subscribe to our newsletter to stay updated with our latest news and insights.</p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0" data-i18n="footer.copyright">© 2025 Sahla Smart Solutions. All rights reserved.</p>
                    <div class="flex space-x-6">
                        <a href="./pages/privacy-policy.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.privacyPolicy">Privacy Policy</a>
                        <a href="./pages/terms-of-service.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.termsOfService">Terms of Service</a>
                        <a href="./pages/cookies-policy.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.cookiesPolicy">Cookies Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Theme switch functionality
        const themeSwitch = document.getElementById('theme-switch');
        const html = document.documentElement;
        const navLogo = document.getElementById('nav-logo');
        const footLogo = document.getElementById('foot-logo');

        // Function to set and save theme
        const setTheme = (theme) => {
            html.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            themeSwitch.classList.toggle('dark', theme === 'dark');

            // Update logo based on theme
            if (theme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        };

        // Set the correct theme class on icon container and logo after load
        document.addEventListener('DOMContentLoaded', () => {
            const currentTheme = html.getAttribute('data-theme');
            themeSwitch.classList.toggle('dark', currentTheme === 'dark');

            // Set initial logo based on theme
            if (currentTheme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        });

        // Toggle theme on click
        themeSwitch.addEventListener('click', () => {
            const isCurrentlyDark = html.getAttribute('data-theme') === 'dark';
            const newTheme = isCurrentlyDark ? 'light' : 'dark';
            setTheme(newTheme);
        });

        // Watch for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                setTheme(e.matches ? 'dark' : 'light');
            }
        });

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navLinks = document.querySelector('.nav-links');

        mobileMenuToggle.addEventListener('change', function() {
            if (this.checked) {
                navLinks.style.display = 'flex';
            } else {
                navLinks.style.display = '';
            }
        });

        // Custom notification functions
        function showNotification(message, type = 'success', duration = 4000) {
            const notif = document.getElementById('custom-notification');
            const msg = document.getElementById('custom-notification-message');
            notif.classList.remove('success', 'error');
            notif.classList.add(type);
            msg.textContent = message;
            notif.style.display = 'flex';
            if (notif.hideTimeout) clearTimeout(notif.hideTimeout);
            notif.hideTimeout = setTimeout(() => {
                notif.style.display = 'none';
            }, duration);
        }

        function hideNotification() {
            const notif = document.getElementById('custom-notification');
            notif.style.display = 'none';
        }

        // Form submission handler
        async function handleSubmit(event) {
            event.preventDefault();

            // Validate form before submission
            if (!validateForm()) {
                showNotification('Please fix the errors in the form before submitting.', 'error');
                return false;
            }


            // Get form data
            const formData = new FormData(event.target);
           

            // Add form type to indicate this is a contact form
            formData.append('form-type', 'contact');

            try {
                // Show loading state on button
                const submitButton = event.target.querySelector('button[type="submit"]');
                const originalButtonText = submitButton.innerHTML;
                submitButton.innerHTML = 'Sending...';
                submitButton.disabled = true;

                // Send the form data to our PHP endpoint
                const response = await fetch('../contact-api/send.php', {
                    method: 'POST',
                    body: formData
                });

                // Check if the response is OK
                if (!response.ok) {
                    throw new Error(`Server responded with status: ${response.status}`);
                }

                // Parse the JSON response
                let result;
                try {
                    const text = await response.text();
                    result = JSON.parse(text);
                } catch (parseError) {
                    console.error('Error parsing server response:', parseError);
                    throw new Error('Invalid response from server');
                }

                if (result.success) {
                    // Redirect to success page
                    showNotification('Your message was sent successfully!', 'success');
                    setTimeout(() => {
                        window.location.href = './contact-success.html';
                    }, 1500);
                } else {
                    console.error('Error submitting form:', result.message);
                    document.getElementById('error-message').style.display = 'block';
                }
            } catch (error) {
                console.error('Error sending email:', error);
                showNotification('There was an error sending your message. Please try again later.', 'error');
            }  finally {
                // Reset button state if it exists
                const submitButton = event.target.querySelector('button[type="submit"]');
                if (submitButton) {
                    submitButton.innerHTML = originalButtonText || 'Send Message';
                    submitButton.disabled = false;
                    document.getElementById('error-message').style.display = 'block';
                }

                // Hide error message after 5 seconds
                setTimeout(() => {
                    document.getElementById('error-message').style.display = 'none';
                }, 5000);
            }


            return false;
        }

        // Custom client-side validation using Validator.js
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('contact-form');
            const fields = {
                name: document.getElementById('full-name'),
                email: document.getElementById('email'),
                phone: document.getElementById('phone'),
                company: document.getElementById('company'),
                service: document.getElementById('service'),
                callTime: document.getElementById('call-time'),
                message: document.getElementById('message')
            };

            function showError(input, message) {
                // Find the closest .form-group parent and its .error-message
                let group = input.closest('.form-group');
                let error = group ? group.querySelector('.error-message') : null;
                if (error) {
                    error.textContent = message;
                    error.style.display = 'block';
                }
                input.classList.add('border-red-500');
            }

            function hideError(input) {
                let group = input.closest('.form-group');
                let error = group ? group.querySelector('.error-message') : null;
                if (error) error.style.display = 'none';
                input.classList.remove('border-red-500');
            }

            function validateField(input) {
                let valid = true;
                if (input === fields.name) {
                    if (!input.value.trim()) {
                        showError(input, 'Please enter your full name');
                        valid = false;
                    } else if (!validator.isLength(input.value.trim(), {min:2, max:50}) || !/^[A-Za-z\s]+$/.test(input.value.trim())) {
                        showError(input, 'Name must be 2-50 letters and spaces only');
                        valid = false;
                    } else {
                        hideError(input);
                    }
                } else if (input === fields.email) {
                    if (!input.value.trim()) {
                        showError(input, 'Please enter a valid email address');
                        valid = false;
                    } else if (!validator.isEmail(input.value.trim())) {
                        showError(input, 'Please enter a valid email address');
                        valid = false;
                    } else {
                        hideError(input);
                    }
                } else if (input === fields.phone) {
                    if (input.value.trim() === "") {
                        hideError(input);
                    } else if (!validator.isMobilePhone(input.value.trim(), undefined, {strictMode: false})) {
                        showError(input, 'Please enter a valid phone number');
                        valid = false;
                    } else {
                        hideError(input);
                    }
                } else if (input === fields.company) {
                    // Company is optional, so just hide any error
                    hideError(input);
                } else if (input === fields.service) {
                    if (!input.value) {
                        showError(input, 'Please select a service');
                        valid = false;
                    } else {
                        hideError(input);
                    }
                } else if (input === fields.callTime) {
                    const phonePreference = document.getElementById('prefer-phone');
                    if (phonePreference.checked && !input.value) {
                        showError(input, 'Please select a preferred call time');
                        valid = false;
                    } else {
                        hideError(input);
                    }
                } else if (input === fields.message) {
                    if (!input.value.trim()) {
                        showError(input, 'Please provide a message');
                        valid = false;
                    } else if (!validator.isLength(input.value.trim(), {min:10})) {
                        showError(input, 'Message must be at least 10 characters');
                        valid = false;
                    } else {
                        hideError(input);
                    }
                }
                return valid;
            }

            window.validateForm = function() {
                let valid = true;
                Object.values(fields).forEach(input => {
                    if (!validateField(input)) valid = false;
                });
                return valid;
            }

            // Validate on input (live feedback)
            Object.values(fields).forEach(input => {
                if (input) {
                    input.addEventListener('input', function() { validateField(input); });
                    input.addEventListener('change', function() { validateField(input); });
                }
            });

            // Validate all on submit
            form.addEventListener('submit', function(e) {
                if (!validateForm()) {
                    e.preventDefault();
                }
            });
        });
    </script>

    <!-- Add EmailJS SDK -->
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
    <script>
        // Initialize EmailJS
        emailjs.init("m3l0WQJTHlQXDWEEG"); // Replace with your EmailJS public key
    </script>

    <!-- i18n Implementation -->
    <script src="../assets/js/i18n.js"></script>
</body>
<script>'undefined'=== typeof _trfq || (window._trfq = []);'undefined'=== typeof _trfd && (window._trfd=[]),_trfd.push({'tccl.baseHost':'secureserver.net'},{'ap':'cpsh-oh'},{'server':'sxb1plzcpnl453516'},{'dcenter':'sxb1'},{'cp_id':'9153305'},{'cp_cache':''},{'cp_cl':'8'}) // Monitoring performance to make your website faster. If you want to opt-out, please contact web hosting support.</script><script src='https://img1.wsimg.com/traffic-assets/js/tccl.min.js'></script>
</html>