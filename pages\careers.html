<!DOCTYPE html>
<html lang="en">

<head>     <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico?v=2">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Careers - Sahla Smart Solutions</title>
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#0056b3',
                        secondary: '#4dabf7'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px',
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/navigation.css">

    <!-- Dynamic Font Loading -->
    <link id="google-fonts" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet">

    <!-- i18next Core and HTTP Backend -->
    <script src="https://unpkg.com/i18next@23.7.16/dist/umd/i18next.min.js"></script>
    <script src="https://unpkg.com/i18next-http-backend@2.4.2/i18nextHttpBackend.min.js"></script>
    <style>
        .careers-hero {
            background-image: linear-gradient(to bottom, #111827d8, #22267a8f), url('https://images.pexels.com/photos/3184432/pexels-photo-3184432.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1&offset=');
            background-size: cover;
            background-position: center top 20%;
            padding: 8rem 0 4rem;
        }

        .job-card {
            border: 1px solid var(--primary-color-1);
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 1.5rem;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .job-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .job-meta {
            display: flex;
            gap: 1.5rem;
            margin: 1rem 0;
            color: var(--text-light);
        }

        .job-meta span {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .career-filters {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .search-box {
            position: relative;
            width: 500px;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 0rem 0.5rem 0rem 0.5rem;
            background-color: var(--background-color-light);
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
        }

        .search-box i {
            font-size: 1rem;
            color: var(--text-light);
        }

        .search-box input {
            border: none;
            outline: none;
            background: transparent;
            font-size: 0.875rem;
            width: 100%;
            color: var(--text-color);
        }

        .search-box input::placeholder {
            color: var(--text-light);
        }

        .filter-container {
            display: flex;
            gap: 1rem;
        }

        .filter-dropdown {
            position: relative;
        }

        .filter-dropdown-btn {
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }

        .filter-dropdown-content {
            position: absolute;
            top: 100%;
            left: 0;
            width: 200px;
            background: var(--bg-light);
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            display: none;
            z-index: 10;
        }

        .filter-dropdown.active .filter-dropdown-content {
            display: block;
        }

        .filter-option {
            background-color: var(--background-color);
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .filter-option:hover {
            background-color: var(--background-color-light);
            color: var(--primary-color-1);
        }

        .benefit-card {
            background: var(--bg-light);
            border-radius: 1rem;
            padding: 2rem;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .benefit-card:hover {
            transform: translateY(-5px);
        }

        .benefit-icon i {
            font-size: 4rem;
        }

        .benefit-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 1.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            color: var(--primary-color-1);
            border-radius: 50%;
            font-size: 1.5rem;
        }

        .culture-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
        }

        .culture-value {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .culture-value i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }

        @media (max-width: 768px) {
            .culture-grid {
                grid-template-columns: 1fr;
            }
            .filter-container {
                flex-direction: column;
            }
        }

        /* Language Toggle Styles */
        .language-toggle-desktop {
            display: flex;
            align-items: center;
            margin-left: 1rem;
        }

        .language-toggle-mobile {
            display: none;
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border-color);
        }

        .language-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            border: 1px solid var(--border-color);
            border-radius: 0.5rem;
            padding: 0.25rem 0.5rem;
            background: var(--background-color);
            transition: all 0.3s ease;
        }

        .lang-btn {
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            background: transparent;
            cursor: pointer;
            color: var(--text-color);
        }

        .lang-btn:hover {
            background-color: var(--primary-color-1);
            color: white;
            transform: translateY(-1px);
            box-shadow: var(--shadow-sm);
        }

        .lang-btn.active {
            background-color: var(--primary-color-1);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .lang-btn.inactive {
            color: var(--text-color-light);
            background: transparent;
        }

        .lang-btn.inactive:hover {
            background-color: var(--background-color-light);
            color: var(--text-color);
        }

        .lang-separator {
            color: var(--text-color-light);
            font-weight: 300;
            transition: color 0.3s ease;
        }

        /* Navigation CTA Button */
        .nav-cta-btn {
            background: var(--primary-color-1);
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .nav-cta-btn:hover {
            background-color: var(--primary-color-2);
            transform: translateY(-1px);
        }

        .nav-divider {
            display: flex;
            align-items: center;
            padding: 0 1rem;
        }

        /* Dark mode specific adjustments */
        [data-theme="dark"] .language-toggle {
            border-color: var(--border-color);
            background: var(--background-color);
        }

        [data-theme="dark"] .lang-btn {
            color: var(--text-color);
        }

        [data-theme="dark"] .lang-btn:hover {
            background-color: var(--primary-color-1);
            color: white;
        }

        [data-theme="dark"] .lang-btn.active {
            background-color: var(--primary-color-1);
            color: white;
        }

        [data-theme="dark"] .lang-btn.inactive {
            color: var(--text-color-light);
            background: transparent;
        }

        [data-theme="dark"] .lang-btn.inactive:hover {
            background-color: var(--background-color-light);
            color: var(--text-color);
        }

        [data-theme="dark"] .lang-separator {
            color: var(--text-color-light);
        }

        /* RTL Support */
        [dir="rtl"] {
            text-align: right;
        }

        /* Navigation RTL fixes */
        [dir="rtl"] .nav {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .nav-links {
            flex-direction: row-reverse;
            gap: 2rem;
        }

        [dir="rtl"] .nav-logo {
            margin-left: 0;
            margin-right: 0;
        }

        [dir="rtl"] .nav-links .dropdown {
            text-align: right;
        }

        [dir="rtl"] .dropdown-content {
            left: auto;
            right: 0;
            text-align: right;
        }

        [dir="rtl"] .dropdown-link {
            text-align: right;
        }

        [dir="rtl"] .nav-divider {
            padding: 0 1rem;
        }

        [dir="rtl"] .language-toggle-desktop {
            margin-left: 0;
            margin-right: 1rem;
        }

        [dir="rtl"] .language-toggle {
            flex-direction: row-reverse;
        }

        /* Mobile RTL Navigation */
        [dir="rtl"] .mobile-menu-button {
            order: -1;
        }

        [dir="rtl"] .nav-links {
            text-align: right;
        }

        [dir="rtl"] .nav-link {
            text-align: right;
        }

        [dir="rtl"] .nav-cta-btn {
            text-align: center;
        }

        /* Mobile Navigation Responsive Styles */
        @media (max-width: 768px) {
            .language-toggle-desktop {
                display: none;
            }

            .language-toggle-mobile {
                display: flex;
                justify-content: center;
            }

            .nav-divider {
                display: none;
            }

            .nav-cta-btn {
                width: 100%;
                text-align: center;
                margin-top: 0.5rem;
            }

            /* RTL Mobile Fixes */
            [dir="rtl"] .nav-links {
                text-align: right;
            }

            [dir="rtl"] .nav-link {
                text-align: right;
            }

            [dir="rtl"] .dropdown-link {
                text-align: right;
            }

            [dir="rtl"] .nav-cta-btn {
                text-align: center;
            }
        }

        /* Arabic Font Support */
        .lang-ar {
            font-family: 'Cairo', sans-serif;
        }

        .lang-en {
            font-family: 'Inter', sans-serif;
        }



        [dir="rtl"] .filter-container {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .search-box {
            flex-direction: row-reverse;
        }



        [dir="rtl"] .job-meta span {
            flex-direction: row-reverse;
        }

        [dir="rtl"] .culture-grid {
            grid-template-columns: 1fr 1fr;
        }

 
        [dir="rtl"] .culture-value i {
            margin-left: 1rem;
            margin-right: 0;
        }

        [dir="rtl"] .filter-dropdown-content {
            left: auto;
            right: 0;
            text-align: right;
        }

        [dir="rtl"] .filter-option {
            text-align: right;
        }

        @media (max-width: 768px) {
            [dir="rtl"] .culture-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <!-- MailerLite Universal -->
    <script>
        (function(w, d, e, u, f, l, n) {
            w[f] = w[f] || function() {
                    (w[f].q = w[f].q || [])
                    .push(arguments);
                }, l = d.createElement(e), l.async = 1, l.src = u,
                n = d.getElementsByTagName(e)[0], n.parentNode.insertBefore(l, n);
        })
        (window, document, 'script', 'https://assets.mailerlite.com/js/universal.js', 'ml');
        ml('account', '1446707');
    </script>
    <!-- End MailerLite Universal -->
</head>

<body class="bg-white">
    <!-- Header & Navigation -->
   <header class="header">
    <div class="container">
      <nav class="nav">
        <a href="../index.html" class="nav-logo">
          <img src="../assets/images/Main Horizontal Version.svg" alt="Sahla Logo" class="logo-img" id="nav-logo">
        </a>
        <input type="checkbox" id="mobile-menu-toggle" class="mobile-menu-toggle">
        <label for="mobile-menu-toggle" class="mobile-menu-button">
          <span></span>
          <span></span>
          <span></span>
        </label>
        <div class="nav-links">
          <div class="dropdown">
            <a href="#services" class="nav-link" data-i18n="nav.services">Services</a>
            <div class="dropdown-content">
              <a href="./process-automation.html" class="dropdown-link" data-i18n="nav.processAutomation">Process Automation</a>
              <a href="./technology-consulting.html" class="dropdown-link" data-i18n="nav.technologyConsulting">Technology Consulting</a>
            </div>
          </div>
          <a href="./ventures.html" class="nav-link" data-i18n="nav.ventures">Our Ventures</a>
          <a href="./partners.html" class="nav-link" data-i18n="nav.partners">Partners</a>
          <a href="./careers.html" class="nav-link" data-i18n="nav.careers">Careers</a>
          <a href="./blog.html" class="nav-link" data-i18n="nav.blog">Blog</a>
          <a href="./about.html" class="nav-link" data-i18n="nav.about">About us</a>
          <div class="nav-divider">
            <div class="h-6 border-l-2 border-gray-300"></div>
          </div>
          <a href="./contact.html" class="nav-cta-btn" data-i18n="nav.contact">Contact Sales</a>

          <!-- Language Toggle - Mobile -->
          <div class="language-toggle-mobile">
            <div class="language-toggle">
              <button id="lang-en-mobile" class="lang-btn" data-lang="en">EN</button>
              <span class="lang-separator">|</span>
              <button id="lang-ar-mobile" class="lang-btn" data-lang="ar">العربية</button>
            </div>
          </div>
          <!-- Language Toggle - Desktop -->
          <div class="language-toggle-desktop">
            <div class="language-toggle">
              <button id="lang-en" class="lang-btn" data-lang="en">EN</button>
              <span class="lang-separator">|</span>
              <button id="lang-ar" class="lang-btn" data-lang="ar">العربية</button>
            </div>
          </div>

          <!-- Theme Switch -->
          <div class="theme-switch" id="theme-switch">
            <i class="ri-moon-line moon-icon"></i>
            <i class="ri-sun-line sun-icon"></i>
            <script>
              (() => {
                const savedTheme = localStorage.getItem('theme');
                const preferred = savedTheme || (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');
                document.documentElement.setAttribute('data-theme', preferred);
              })();
            </script>
          </div>
        </div>
      </nav>
    </div>
  </header>

    <!-- Careers Hero Section -->
    <section class="careers-hero">
        <div class="container">
            <div class="text-center text-white">
                <h1 class="h1 mb-4 flex" style="margin-top: 10%;" data-i18n="careers.hero.title">Join Our Team</h1>
                <p class="text-xl max-w-2xl" style="color: #e5e7eb; text-align: start;" data-i18n="careers.hero.subtitle">Build the future of technology in MENA with Sahla Smart Solutions. We're looking for exceptional talent to join our growing team.</p>
            </div>
        </div>
    </section>

    <!-- Current Openings Section -->
    <section class="section">
        <div class="container">
            <div class="text-center mb-16">
                <h2 class="section-title !text-center" data-i18n="careers.currentOpenings">Current Openings</h2>
                <p class="section-subtitle  !text-center" data-i18n="careers.findRole">Find your perfect role and grow with us</p>
            </div>

            <div class="career-filters">
                <div class="filter-container">
                    <div class="filter-dropdown" id="department-filter">
                        <button class="filter-dropdown-btn">
                            <span data-i18n="careers.filters.allDepartments">All Departments</span> <i class="ri-arrow-down-s-line"></i>
                        </button>
                        <div class="filter-dropdown-content">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="filter-dropdown" id="location-filter">
                        <button class="filter-dropdown-btn">
                            <span data-i18n="careers.filters.allLocations">All Locations</span> <i class="ri-arrow-down-s-line"></i>
                        </button>
                        <div class="filter-dropdown-content">
                            <!-- Will be populated by JavaScript -->
                        </div>
                    </div>
                    <div class="search-box flex items-center">
                        <i class="ri-search-line"></i>
                        <input type="text" id="job-search" data-i18n-placeholder="careers.filters.searchPlaceholder" placeholder="Search positions..." class="form-input my-auto">
                    </div>
                </div>
            </div>

            <div id="job-listings" class="job-listings">
                <!-- Will be populated by JavaScript -->
            </div>
            <div class="flex justify-center">
                <p class="text-gray-400 text-sm mb-4 md:mb-0" data-i18n="careers.disclaimer">Kindly note that all responsibilities, requirements, etc displayed are indicative and may change based on the needs of the role and the business. You will be provided a more detailed job description upon selection of a role and a meeting with the hiring manager.</p>
            </div>
        </div>
    </section>

    <!-- Why Join Us Section -->
    <section class="section bg-light">
        <div class="container">
            <div class="text-center mb-16">
                <h2 class="section-title !text-center" data-i18n="careers.whyJoinUs">Why Join Sahla?</h2>
                <p class="section-subtitle !text-center" data-i18n="careers.whyJoinSubtitle">We're building the future of technology in MENA, and we need exceptional talent to make it happen</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="ri-rocket-2-line"></i>
                    </div>
                    <h3 class="h3" data-i18n="careers.benefits.innovation.title">Innovation-Driven Culture</h3>
                    <p data-i18n="careers.benefits.innovation.description">Work on cutting-edge technologies and business models that shape the future of industries.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="ri-team-line"></i>
                    </div>
                    <h3 class="h3" data-i18n="careers.benefits.diverse.title">Diverse & Inclusive Team</h3>
                    <p data-i18n="careers.benefits.diverse.description">Join a diverse team of professionals from over 20 nationalities and various backgrounds.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="ri-arrow-up-circle-line"></i>
                    </div>
                    <h3 class="h3" data-i18n="careers.benefits.growth.title">Growth Opportunities</h3>
                    <p data-i18n="careers.benefits.growth.description">Continuous learning and development with clear career progression paths.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="ri-global-line"></i>
                    </div>
                    <h3 class="h3" data-i18n="careers.benefits.impact.title">Regional Impact</h3>
                    <p data-i18n="careers.benefits.impact.description">Make a meaningful impact on the tech ecosystem across the MENA region.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="ri-heart-pulse-line"></i>
                    </div>
                    <h3 class="h3" data-i18n="careers.benefits.benefits.title">Comprehensive Benefits</h3>
                    <p data-i18n="careers.benefits.benefits.description">Competitive compensation, health insurance, and flexible work arrangements.</p>
                </div>

                <div class="benefit-card">
                    <div class="benefit-icon">
                        <i class="ri-community-line"></i>
                    </div>
                    <h3 class="h3" data-i18n="careers.benefits.collaborative.title">Collaborative Environment</h3>
                    <p data-i18n="careers.benefits.collaborative.description">Work with passionate individuals in a supportive and collaborative workplace.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Our Culture Section -->
    <section class="section">
        <div class="container">
            <div class="text-center mb-16">
                <h2 class="section-title !text-center" data-i18n="careers.culture.title">Our Culture</h2>
                <p class="section-subtitle !text-center" data-i18n="careers.culture.subtitle">What makes working at Sahla unique</p>
            </div>

            <div class="culture-grid">
                <div class="culture-image">
                    <img src="https://media.istockphoto.com/id/1322842973/photo/diverse-business-people-putting-their-hands-together-in-cirle.jpg?s=612x612&w=0&k=20&c=9BAYCv8tAsgYPQdTsFxLzLJsmt6tGYE5Etwd63OccxQ=" alt="Sahla Team Culture" class="rounded-img">
                </div>
                <div class="culture-content">
                    <div class="culture-value">
                        <i class="ri-check-double-line"></i>
                        <div>
                            <h3 class="h3" data-i18n="careers.culture.values.innovation.title">Innovation First</h3>
                            <p data-i18n="careers.culture.values.innovation.description">We embrace innovative thinking and encourage creative solutions to complex problems.</p>
                        </div>
                    </div>

                    <div class="culture-value">
                        <i class="ri-check-double-line"></i>
                        <div>
                            <h3 class="h3" data-i18n="careers.culture.values.customer.title">Customer-Centricity</h3>
                            <p data-i18n="careers.culture.values.customer.description">We put our customers and their needs at the center of everything we do.</p>
                        </div>
                    </div>

                    <div class="culture-value">
                        <i class="ri-check-double-line"></i>
                        <div>
                            <h3 class="h3" data-i18n="careers.culture.values.ownership.title">Ownership Mindset</h3>
                            <p data-i18n="careers.culture.values.ownership.description">We take responsibility for our work and act as owners, not just employees.</p>
                        </div>
                    </div>

                    <div class="culture-value">
                        <i class="ri-check-double-line"></i>
                        <div>
                            <h3 class="h3" data-i18n="careers.culture.values.learning.title">Continuous Learning</h3>
                            <p data-i18n="careers.culture.values.learning.description">We're committed to continuous improvement and lifelong learning.</p>
                        </div>
                    </div>

                    <div class="culture-value">
                        <i class="ri-check-double-line"></i>
                        <div>
                            <h3 class="h3" data-i18n="careers.culture.values.collaborative.title">Collaborative Spirit</h3>
                            <p data-i18n="careers.culture.values.collaborative.description">We believe in the power of teamwork and knowledge sharing.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action -->
    <section class="cta">
        <div class="container">
            <h2 class="h2" data-i18n="careers.cta.title">Ready to Join Our Team?</h2>
            <p class="cta-text" data-i18n="careers.cta.subtitle">Explore our current openings and take the next step in your career.</p>
            <a href="#job-listings" class="btn btn-light" data-i18n="careers.cta.button">View All Positions</a>
        </div>
    </section>

    <!-- f -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Company Info Column -->
                <div class="footer-info">
                    <a href="../index.html" class="footer-logo">
                        <img src="../assets/images/Negative Horizontal Version.svg" alt="Sahla Smart Solutions" class="logo-img" id="foot-logo">
                    </a>
                    <p class="footer-description" data-i18n="footer.description">Empowering businesses through innovative venture building and automation solutions in the MENA region.</p>
                    <div class="footer-social">
                        <a href="https://eg.linkedin.com/company/sahla-solutions" target="_blank" rel="noopener noreferrer"><i class="ri-linkedin-fill"></i></a>
                        <a href="https://x.com/sahlasolutions" target="_blank" rel="noopener noreferrer"><i class="ri-twitter-fill"></i></a>
                        <a href="https://www.facebook.com/SahlaSmartSolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-facebook-fill"></i></a>
                        <a href="https://www.instagram.com/sahlasolutions/" target="_blank" rel="noopener noreferrer"><i class="ri-instagram-fill"></i></a>
                    </div>
                </div>

                <!-- Quick Links Column -->
                <div class="footer-links-column">
                    <h4 data-i18n="footer.quickLinks">Quick Links</h4>
                    <ul class="footer-links-list">
                        <li><a href="../index.html" data-i18n="footer.home">Home</a></li>
                        <li><a href="./about.html" data-i18n="footer.aboutUs">About Us</a></li>
                        <li><a href="./ventures.html" data-i18n="footer.ourVentures">Our Ventures</a></li>
                        <li><a href="./partners.html" data-i18n="nav.partners">Partners</a></li>
                        <li><a href="./careers.html" data-i18n="nav.careers">Careers</a></li>
                        <li><a href="./contact.html" data-i18n="footer.contactUs">Contact Us</a></li>
                    </ul>
                </div>

                <!-- Services Column -->
                <div class="footer-links-column">
                    <h4 data-i18n="footer.services">Services</h4>
                    <ul class="footer-links-list">
                        <li><a href="./process-automation.html" data-i18n="nav.processAutomation">Process Automation</a></li>
                        <li><a href="./technology-consulting.html" data-i18n="nav.technologyConsulting">Technology Consulting</a></li>
                    </ul>
                </div>

                <!-- Newsletter Column -->
                <div class="footer-newsletter">
                    <h4 data-i18n="footer.newsletter">Newsletter</h4>

                    <form class="mb-4">
                        <div class="flex">
                            <!-- <input type="email" placeholder="Your email" class="w-full px-4 py-2 bg-gray-800 border-none text-white text-sm rounded-l"> -->
                            <a href="javascript:void(0)" class="ml-onclick-form" onclick="ml('show', 'gturk0', true)" style="
                                display: inline-block;
                                padding: 8px 16px;
                                background-color: var(--primary-color-1);
                                color: white;
                                font-size: 16px;
                                font-family: sans-serif;
                                border: none;
                                border-radius: 6px;
                                text-decoration: none;
                                cursor: pointer;
                                transition: background-color 0.3s ease;
                                " onmouseover="this.style.backgroundColor='var(--primary-color-2)'" onmouseout="this.style.backgroundColor= 'var(--primary-color-3)'" data-i18n="footer.subscribeNewsletter">
                                Subscribe to Newsletter
                                </a>
                        </div>
                    </form>
                    <p data-i18n="footer.newsletterDescription">Subscribe to our newsletter to stay updated with our latest news and insights.</p>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-400 text-sm mb-4 md:mb-0" data-i18n="footer.copyright">© 2025 Sahla Smart Solutions. All rights reserved.</p>
                    <div class="flex space-x-6">
                        <a href="./pages/privacy-policy.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.privacyPolicy">Privacy Policy</a>
                        <a href="./pages/terms-of-service.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.termsOfService">Terms of Service</a>
                        <a href="./pages/cookies-policy.html" class="text-gray-400 hover:text-white text-sm" data-i18n="footer.cookiesPolicy">Cookies Policy</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Jobs Data -->
    <script src="../assets/js/jobs-data.js?v=1.0.1"></script>

    <script>
        // Theme switch functionality
        const themeSwitch = document.getElementById('theme-switch');
        const html = document.documentElement;
        const navLogo = document.getElementById('nav-logo');
        const footLogo = document.getElementById('foot-logo');

        // Function to set and save theme
        const setTheme = (theme) => {
            html.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            themeSwitch.classList.toggle('dark', theme === 'dark');

            // Update logo based on theme
            if (theme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        };

        // Set the correct theme class on icon container and logo after load
        document.addEventListener('DOMContentLoaded', () => {
            const currentTheme = html.getAttribute('data-theme');
            themeSwitch.classList.toggle('dark', currentTheme === 'dark');

            // Set initial logo based on theme
            if (currentTheme === 'dark') {
                navLogo.src = '../assets/images/Dark Horizontal Version.svg';
                footLogo.src = '../assets/images/Dark Horizontal Version.svg';
            } else {
                navLogo.src = '../assets/images/Main Horizontal Version.svg';
                footLogo.src = '../assets/images/Main Horizontal Version.svg';
            }
        });

        // Toggle theme on click
        themeSwitch.addEventListener('click', () => {
            const isCurrentlyDark = html.getAttribute('data-theme') === 'dark';
            const newTheme = isCurrentlyDark ? 'light' : 'dark';
            setTheme(newTheme);
        });

        // Watch for system theme changes
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
            if (!localStorage.getItem('theme')) {
                setTheme(e.matches ? 'dark' : 'light');
            }
        });

        // Mobile menu toggle
        const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
        const navLinks = document.querySelector('.nav-links');

        mobileMenuToggle.addEventListener('change', function() {
            if (this.checked) {
                navLinks.style.display = 'flex';
            } else {
                navLinks.style.display = '';
            }
        });

        // Initialize job listings
        function initializeJobListings() {
            const jobListingsContainer = document.getElementById('job-listings');
            const departmentFilter = document.getElementById('department-filter');
            const locationFilter = document.getElementById('location-filter');
            const searchInput = document.getElementById('job-search');

            let currentDepartment = 'all';
            let currentLocation = 'all';
            let searchQuery = '';

            // Populate department filter
            const departmentContent = departmentFilter.querySelector('.filter-dropdown-content');
            jobsData.departments.forEach(dept => {
                const option = document.createElement('div');
                option.className = 'filter-option';
                option.textContent = dept.name;
                option.dataset.value = dept.id;
                option.addEventListener('click', () => {
                    currentDepartment = dept.id;
                    const btnSpan = departmentFilter.querySelector('.filter-dropdown-btn span');
                    btnSpan.textContent = dept.name;
                    departmentFilter.classList.remove('active');
                    filterJobs();
                });
                departmentContent.appendChild(option);
            });

            // Populate location filter
            const locationContent = locationFilter.querySelector('.filter-dropdown-content');
            jobsData.locations.forEach(loc => {
                const option = document.createElement('div');
                option.className = 'filter-option';
                option.textContent = loc.name;
                option.dataset.value = loc.id;
                option.addEventListener('click', () => {
                    currentLocation = loc.id;
                    const btnSpan = locationFilter.querySelector('.filter-dropdown-btn span');
                    btnSpan.textContent = loc.name;
                    locationFilter.classList.remove('active');
                    filterJobs();
                });
                locationContent.appendChild(option);
            });

            // Toggle dropdowns
            document.querySelectorAll('.filter-dropdown-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const dropdown = btn.parentElement;
                    document.querySelectorAll('.filter-dropdown').forEach(d => {
                        if (d !== dropdown) d.classList.remove('active');
                    });
                    dropdown.classList.toggle('active');
                });
            });

            // Search functionality
            searchInput.addEventListener('input', (e) => {
                searchQuery = e.target.value.toLowerCase();
                filterJobs();
            });

            // Close dropdowns when clicking outside
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.filter-dropdown')) {
                    document.querySelectorAll('.filter-dropdown').forEach(d => {
                        d.classList.remove('active');
                    });
                }
            });

            // Filter and display jobs
            function filterJobs() {
                const filteredJobs = jobsData.jobs.filter(job => {
                    const matchesDepartment = currentDepartment === 'all' || job.department === currentDepartment;
                    const matchesLocation = currentLocation === 'all' || job.location === currentLocation;
                    const matchesSearch = searchQuery === '' ||
                        job.title.toLowerCase().includes(searchQuery) ||
                        job.description.toLowerCase().includes(searchQuery);

                    return matchesDepartment && matchesLocation && matchesSearch;
                });

                displayJobs(filteredJobs);
            }

            // Display jobs
            function displayJobs(jobs) {
                jobListingsContainer.innerHTML = '';

                if (jobs.length === 0) {
                    const noPositionsText = i18next.t('careers.jobListing.noPositions') || 'No positions found matching your criteria.';
                    jobListingsContainer.innerHTML = `
                        <div class="text-center py-8">
                            <p class="text-xl text-gray-600">${noPositionsText}</p>
                        </div>
                    `;
                    return;
                }

                jobs.forEach(job => {
                    const department = jobsData.departments.find(d => d.id === job.department);
                    const location = jobsData.locations.find(l => l.id === job.location);
                    const applyNowText = i18next.t('careers.jobListing.applyNow') || 'Apply Now';

                    const jobCard = document.createElement('div');
                    jobCard.className = 'job-card';
                    jobCard.innerHTML = `
                        <div class="job-details">
                            <h3 class="h3">${job.title}</h3>
                            <div class="job-meta">
                                <span><i class="ri-building-line"></i> ${department.name}</span>
                                <span><i class="ri-map-pin-line"></i> ${location.name}</span>
                                <span><i class="ri-time-line"></i> ${job.type}</span>
                            </div>
                            <p class="text-gray-600">${job.description}</p>
                        </div>
                        <div class="mt-4">
                            <a href="/pages/career-application.html?job=${job.id}" class="btn btn-primary">${applyNowText}</a>
                        </div>
                    `;
                    jobListingsContainer.appendChild(jobCard);
                });
            }

            // Initial display
            filterJobs();
        }

        // Function to update filter dropdown texts when language changes
        function updateFilterTexts() {
            const departmentBtn = document.querySelector('#department-filter .filter-dropdown-btn span');
            const locationBtn = document.querySelector('#location-filter .filter-dropdown-btn span');

            if (departmentBtn && departmentBtn.getAttribute('data-i18n') === 'careers.filters.allDepartments') {
                departmentBtn.textContent = i18next.t('careers.filters.allDepartments') || 'All Departments';
            }

            if (locationBtn && locationBtn.getAttribute('data-i18n') === 'careers.filters.allLocations') {
                locationBtn.textContent = i18next.t('careers.filters.allLocations') || 'All Locations';
            }
        }

        // Listen for language changes
        window.addEventListener('languageChanged', () => {
            updateFilterTexts();
            // Re-render job listings to update "Apply Now" buttons
            if (typeof filterJobs === 'function') {
                filterJobs();
            }
        });

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            initializeJobListings();
        });
    </script>

    <!-- i18n Implementation -->
    <script src="../assets/js/i18n.js"></script>
</body>

</html>